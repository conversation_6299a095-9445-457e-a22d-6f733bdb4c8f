#!/usr/bin/env python3
"""
环境设置脚本
安装YOLOv11训练所需的依赖包
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"{description}")
    print(f"{'='*60}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print("✓ 成功")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 失败: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def check_conda_env():
    """检查是否在正确的conda环境中"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env != 'efficientdet':
        print(f"警告: 当前conda环境是 '{conda_env}'，建议使用 'efficientdet' 环境")
        print("请运行: source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet")
        return False
    else:
        print(f"✓ 当前conda环境: {conda_env}")
        return True

def install_packages():
    """安装必要的Python包"""
    packages = [
        "ultralytics",  # YOLOv11
        "opencv-python",  # OpenCV
        "Pillow",  # 图像处理
        "matplotlib",  # 可视化
        "seaborn",  # 统计可视化
        "tqdm",  # 进度条
        "pyyaml",  # YAML处理
        "tensorboard",  # 训练监控
    ]
    
    print(f"\n{'='*60}")
    print("安装Python包")
    print(f"{'='*60}")
    
    for package in packages:
        print(f"\n安装 {package}...")
        success = run_command(f"pip install {package}", f"安装 {package}")
        if not success:
            print(f"警告: {package} 安装失败，可能需要手动安装")
    
    # 检查CUDA版本并安装对应的PyTorch
    print(f"\n{'='*60}")
    print("检查PyTorch和CUDA")
    print(f"{'='*60}")
    
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ CUDA版本: {torch.version.cuda}")
            print(f"✓ GPU设备: {torch.cuda.get_device_name(0)}")
    except ImportError:
        print("PyTorch未安装，正在安装...")
        # 安装PyTorch (CUDA版本)
        run_command("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118", 
                   "安装PyTorch (CUDA 11.8)")

def create_project_structure():
    """创建项目目录结构"""
    print(f"\n{'='*60}")
    print("创建项目目录结构")
    print(f"{'='*60}")
    
    base_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11s")
    
    directories = [
        "dataset/images/train",
        "dataset/images/val", 
        "dataset/images/test",
        "dataset/labels/train",
        "dataset/labels/val",
        "dataset/labels/test",
        "runs/train",
        "runs/val",
        "runs/inference",
        "scripts",
        "configs",
        "docs"
    ]
    
    for dir_path in directories:
        full_path = base_dir / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录: {full_path}")

def download_yolo_weights():
    """下载YOLOv11预训练权重"""
    print(f"\n{'='*60}")
    print("下载YOLOv11预训练权重")
    print(f"{'='*60}")
    
    try:
        from ultralytics import YOLO
        
        # 下载YOLOv11s权重
        print("下载 yolo11s.pt...")
        model = YOLO('yolo11s.pt')
        print("✓ yolo11s.pt 下载完成")
        
        # 下载其他版本（可选）
        models = ['yolo11n.pt', 'yolo11m.pt', 'yolo11l.pt']
        for model_name in models:
            try:
                print(f"下载 {model_name}...")
                YOLO(model_name)
                print(f"✓ {model_name} 下载完成")
            except Exception as e:
                print(f"⚠ {model_name} 下载失败: {e}")
                
    except ImportError:
        print("✗ ultralytics未安装，无法下载权重文件")

def create_requirements_file():
    """创建requirements.txt文件"""
    requirements = """# YOLOv11 Training Requirements
ultralytics>=8.0.0
opencv-python>=4.5.0
Pillow>=8.0.0
matplotlib>=3.3.0
seaborn>=0.11.0
tqdm>=4.60.0
PyYAML>=5.4.0
tensorboard>=2.8.0
torch>=1.12.0
torchvision>=0.13.0
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0
"""
    
    requirements_path = Path("/home/<USER>/claude/SpatialVLA/3D/yolo11s/requirements.txt")
    with open(requirements_path, 'w') as f:
        f.write(requirements)
    
    print(f"✓ 创建requirements.txt: {requirements_path}")

def main():
    print("YOLOv11 环境设置脚本")
    print("=" * 60)
    
    # 检查conda环境
    check_conda_env()
    
    # 创建项目结构
    create_project_structure()
    
    # 创建requirements文件
    create_requirements_file()
    
    # 安装包
    install_packages()
    
    # 下载预训练权重
    download_yolo_weights()
    
    print(f"\n{'='*60}")
    print("环境设置完成！")
    print(f"{'='*60}")
    print("接下来的步骤:")
    print("1. 运行数据转换脚本: python scripts/convert_coco_to_yolo.py")
    print("2. 开始训练: python scripts/train_yolo11s.py")
    print("3. 进行推理: python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --source test_image.jpg")

if __name__ == "__main__":
    main()