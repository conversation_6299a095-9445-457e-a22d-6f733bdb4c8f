#!/bin/bash

# YOLOv11s 训练启动脚本
# 用于机器人视觉竞赛3D识别项目

echo "=========================================="
echo "YOLOv11s RGB训练项目启动"
echo "机器人视觉竞赛3D识别项目"
echo "=========================================="

# 检查conda环境
if [ "$CONDA_DEFAULT_ENV" != "efficientdet" ]; then
    echo "警告: 当前不在efficientdet环境中"
    echo "正在激活efficientdet环境..."
    source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet
    
    if [ "$CONDA_DEFAULT_ENV" != "efficientdet" ]; then
        echo "错误: 无法激活efficientdet环境"
        echo "请手动运行: source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet"
        exit 1
    fi
fi

echo "✓ 当前conda环境: $CONDA_DEFAULT_ENV"

# 切换到项目目录
cd /home/<USER>/claude/SpatialVLA/3D/yolo11s

echo "✓ 项目目录: $(pwd)"

# 检查Python和必要包
echo "检查Python环境..."
python -c "import sys; print(f'Python版本: {sys.version}')"

# 检查关键包
echo "检查必要的包..."
python -c "
try:
    import ultralytics
    print('✓ ultralytics 已安装')
except ImportError:
    print('✗ ultralytics 未安装')
    exit(1)

try:
    import torch
    print(f'✓ PyTorch 已安装: {torch.__version__}')
    print(f'✓ CUDA 可用: {torch.cuda.is_available()}')
    if torch.cuda.is_available():
        print(f'✓ GPU: {torch.cuda.get_device_name(0)}')
except ImportError:
    print('✗ PyTorch 未安装')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "环境检查失败，正在安装依赖..."
    python scripts/setup_environment.py
    
    if [ $? -ne 0 ]; then
        echo "依赖安装失败，请手动检查"
        exit 1
    fi
fi

echo ""
echo "=========================================="
echo "开始训练流水线"
echo "=========================================="

# 运行主训练脚本
python run_training.py "$@"

echo ""
echo "=========================================="
echo "训练完成"
echo "=========================================="

# 显示结果
if [ -d "runs/train" ]; then
    echo "训练结果保存在: runs/train/"
    
    # 查找最新的训练结果
    LATEST_RUN=$(ls -t runs/train/ | head -n 1)
    if [ -n "$LATEST_RUN" ]; then
        echo "最新训练: runs/train/$LATEST_RUN"
        
        if [ -f "runs/train/$LATEST_RUN/weights/best.pt" ]; then
            echo "最佳模型: runs/train/$LATEST_RUN/weights/best.pt"
            echo ""
            echo "使用模型进行推理:"
            echo "python scripts/inference.py --model runs/train/$LATEST_RUN/weights/best.pt --source your_image.jpg"
        fi
    fi
fi

echo ""
echo "如需查看详细结果，请检查 runs/ 目录"
echo "如需重新训练，请再次运行此脚本"