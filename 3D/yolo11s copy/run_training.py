#!/usr/bin/env python3
"""
YOLOv11s 训练主脚本
一键完成数据转换、环境检查和模型训练
"""

import os
import sys
import subprocess
from pathlib import Path
import argparse

def run_command(command, description, cwd=None):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"{description}")
    print(f"{'='*60}")
    print(f"执行命令: {command}")
    if cwd:
        print(f"工作目录: {cwd}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              cwd=cwd, text=True)
        print("✓ 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 失败: {e}")
        return False

def check_environment():
    """检查运行环境"""
    print("检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or python_version.minor < 8:
        print("警告: 建议使用Python 3.8或更高版本")
    
    # 检查conda环境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    print(f"Conda环境: {conda_env}")
    
    if conda_env != 'efficientdet':
        print("警告: 建议在efficientdet环境中运行")
        print("请运行: source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet")
    
    # 检查必要的包
    required_packages = ['ultralytics', 'torch', 'cv2', 'PIL', 'yaml']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                import PIL
            elif package == 'yaml':
                import yaml
            else:
                __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少包: {missing_packages}")
        print("请先运行: python scripts/setup_environment.py")
        return False
    
    return True

def convert_dataset():
    """转换数据集格式"""
    script_path = Path(__file__).parent / "scripts" / "convert_coco_to_yolo.py"
    
    if not script_path.exists():
        print(f"错误: 转换脚本不存在: {script_path}")
        return False
    
    command = f"python {script_path}"
    return run_command(command, "转换COCO数据集为YOLO格式")

def train_model(epochs=100, batch_size=16, device='auto'):
    """训练模型"""
    script_path = Path(__file__).parent / "scripts" / "train_yolo11s.py"
    dataset_yaml = Path(__file__).parent / "dataset" / "dataset.yaml"
    
    if not script_path.exists():
        print(f"错误: 训练脚本不存在: {script_path}")
        return False
    
    if not dataset_yaml.exists():
        print(f"错误: 数据集配置文件不存在: {dataset_yaml}")
        print("请先运行数据转换步骤")
        return False
    
    command = f"python {script_path} --data {dataset_yaml} --epochs {epochs} --batch-size {batch_size} --device {device}"
    return run_command(command, f"训练YOLOv11s模型 (epochs={epochs}, batch_size={batch_size})")

def validate_model():
    """验证训练好的模型"""
    # 查找最新的训练结果
    runs_dir = Path(__file__).parent / "runs" / "train"
    
    if not runs_dir.exists():
        print("错误: 未找到训练结果目录")
        return False
    
    # 查找最新的实验目录
    experiment_dirs = [d for d in runs_dir.iterdir() if d.is_dir()]
    if not experiment_dirs:
        print("错误: 未找到训练实验")
        return False
    
    # 按修改时间排序，获取最新的
    latest_experiment = max(experiment_dirs, key=lambda x: x.stat().st_mtime)
    best_model = latest_experiment / "weights" / "best.pt"
    
    if not best_model.exists():
        print(f"错误: 未找到最佳模型: {best_model}")
        return False
    
    # 运行验证
    script_path = Path(__file__).parent / "scripts" / "train_yolo11s.py"
    dataset_yaml = Path(__file__).parent / "dataset" / "dataset.yaml"
    
    command = f"python {script_path} --validate --model {best_model} --data {dataset_yaml}"
    return run_command(command, "验证训练好的模型")

def run_inference_test():
    """运行推理测试"""
    # 查找最新的训练结果
    runs_dir = Path(__file__).parent / "runs" / "train"
    
    if not runs_dir.exists():
        print("错误: 未找到训练结果目录")
        return False
    
    experiment_dirs = [d for d in runs_dir.iterdir() if d.is_dir()]
    if not experiment_dirs:
        print("错误: 未找到训练实验")
        return False
    
    latest_experiment = max(experiment_dirs, key=lambda x: x.stat().st_mtime)
    best_model = latest_experiment / "weights" / "best.pt"
    
    if not best_model.exists():
        print(f"错误: 未找到最佳模型: {best_model}")
        return False
    
    # 使用测试集进行推理
    test_images_dir = Path(__file__).parent / "dataset" / "images" / "test"
    
    if not test_images_dir.exists() or not any(test_images_dir.iterdir()):
        print("警告: 测试图像目录为空，跳过推理测试")
        return True
    
    script_path = Path(__file__).parent / "scripts" / "inference.py"
    output_dir = Path(__file__).parent / "runs" / "inference" / "test_results"
    
    command = f"python {script_path} --model {best_model} --source {test_images_dir} --output {output_dir} --no-show"
    return run_command(command, "运行推理测试")

def main():
    parser = argparse.ArgumentParser(description='YOLOv11s Training Pipeline')
    parser.add_argument('--skip-convert', action='store_true',
                       help='Skip dataset conversion step')
    parser.add_argument('--skip-train', action='store_true',
                       help='Skip training step')
    parser.add_argument('--skip-validate', action='store_true',
                       help='Skip validation step')
    parser.add_argument('--skip-inference', action='store_true',
                       help='Skip inference test step')
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=16,
                       help='Training batch size')
    parser.add_argument('--device', type=str, default='auto',
                       help='Training device (auto, cpu, 0, 1, ...)')
    
    args = parser.parse_args()
    
    print("YOLOv11s 训练流水线")
    print("=" * 60)
    print("用于机器人视觉竞赛3D识别项目")
    print("去除深度数据，仅使用RGB图像训练")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n环境检查失败，请先运行: python scripts/setup_environment.py")
        sys.exit(1)
    
    success_steps = []
    failed_steps = []
    
    # 步骤1: 数据转换
    if not args.skip_convert:
        print(f"\n{'='*60}")
        print("步骤 1/4: 数据集转换")
        print(f"{'='*60}")
        if convert_dataset():
            success_steps.append("数据转换")
        else:
            failed_steps.append("数据转换")
            print("数据转换失败，停止执行")
            sys.exit(1)
    else:
        print("跳过数据转换步骤")
    
    # 步骤2: 模型训练
    if not args.skip_train:
        print(f"\n{'='*60}")
        print("步骤 2/4: 模型训练")
        print(f"{'='*60}")
        if train_model(args.epochs, args.batch_size, args.device):
            success_steps.append("模型训练")
        else:
            failed_steps.append("模型训练")
    else:
        print("跳过模型训练步骤")
    
    # 步骤3: 模型验证
    if not args.skip_validate:
        print(f"\n{'='*60}")
        print("步骤 3/4: 模型验证")
        print(f"{'='*60}")
        if validate_model():
            success_steps.append("模型验证")
        else:
            failed_steps.append("模型验证")
    else:
        print("跳过模型验证步骤")
    
    # 步骤4: 推理测试
    if not args.skip_inference:
        print(f"\n{'='*60}")
        print("步骤 4/4: 推理测试")
        print(f"{'='*60}")
        if run_inference_test():
            success_steps.append("推理测试")
        else:
            failed_steps.append("推理测试")
    else:
        print("跳过推理测试步骤")
    
    # 总结
    print(f"\n{'='*60}")
    print("执行总结")
    print(f"{'='*60}")
    
    if success_steps:
        print("✓ 成功完成的步骤:")
        for step in success_steps:
            print(f"  - {step}")
    
    if failed_steps:
        print("✗ 失败的步骤:")
        for step in failed_steps:
            print(f"  - {step}")
    
    if not failed_steps:
        print("\n🎉 所有步骤执行成功！")
        
        # 显示结果位置
        runs_dir = Path(__file__).parent / "runs"
        if runs_dir.exists():
            print(f"\n训练结果保存在: {runs_dir}")
            
            # 查找最新的训练结果
            train_dir = runs_dir / "train"
            if train_dir.exists():
                experiment_dirs = [d for d in train_dir.iterdir() if d.is_dir()]
                if experiment_dirs:
                    latest_experiment = max(experiment_dirs, key=lambda x: x.stat().st_mtime)
                    best_model = latest_experiment / "weights" / "best.pt"
                    if best_model.exists():
                        print(f"最佳模型: {best_model}")
                        print(f"\n使用模型进行推理:")
                        print(f"python scripts/inference.py --model {best_model} --source your_image.jpg")
    else:
        print(f"\n❌ 有 {len(failed_steps)} 个步骤失败，请检查错误信息")

if __name__ == "__main__":
    main()