# YOLOv11s 快速开始指南

## 🚀 一键启动训练

### 方法1: 使用启动脚本（推荐）
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo11s
./start_training.sh
```

### 方法2: 使用Python脚本
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo11s
source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet
python run_training.py
```

## 📋 项目特点

✅ **已完成的工作**:
- 去除了所有深度(Deep)数据，仅保留RGB图像
- 将COCO格式标注转换为YOLO格式
- 配置了YOLOv11s模型训练参数
- 针对机器人视觉竞赛进行了优化

✅ **支持的功能**:
- 自动数据格式转换
- 一键训练流水线
- 模型验证和推理
- 批量图像处理
- 实时摄像头推理

## 🎯 识别类别

支持9个竞赛类别：
1. CA001_衣架 (日用品)
2. CA002_牙刷 (日用品)  
3. CB001_果冻 (副食品)
4. CB002_长方形状饼干 (副食品)
5. CC001_罐装饮料 (饮料)
6. CC002_瓶装饮料 (饮料)
7. CD001_香蕉 (水果)
8. CD002_橙子 (水果)
9. Wxxx_未知物品 (未知类)

## ⚙️ 训练参数

默认配置（可根据硬件调整）：
- **模型**: YOLOv11s (Small版本，平衡速度与精度)
- **训练轮数**: 100 epochs
- **批次大小**: 16 (可根据GPU内存调整)
- **图像尺寸**: 640x640
- **学习率**: 自适应调整
- **数据增强**: 启用多种增强策略

## 📊 预期性能

基于YOLOv11s的预期性能：
- **推理速度**: ~2-5ms (GPU) / ~50-100ms (CPU)
- **模型大小**: ~22MB
- **精度**: mAP50 预期 > 0.8
- **内存占用**: ~2GB (训练) / ~500MB (推理)

## 🔧 自定义训练

### 调整训练参数
```bash
python run_training.py --epochs 200 --batch-size 32 --device 0
```

### 仅转换数据（跳过训练）
```bash
python run_training.py --skip-train
```

### 仅验证模型
```bash
python scripts/train_yolo11s.py --validate --model runs/train/yolo11s_rgb/weights/best.pt --data dataset/dataset.yaml
```

## 🖼️ 推理使用

### 单张图像
```bash
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --source test.jpg
```

### 批量处理
```bash
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --source test_images/ --output results/
```

### 实时摄像头
```bash
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --camera
```

## 📁 重要文件位置

- **训练结果**: `runs/train/yolo11s_rgb/`
- **最佳模型**: `runs/train/yolo11s_rgb/weights/best.pt`
- **数据集配置**: `dataset/dataset.yaml`
- **训练日志**: `runs/train/yolo11s_rgb/train_batch*.jpg`
- **验证结果**: `runs/train/yolo11s_rgb/val_batch*.jpg`

## 🏆 竞赛部署建议

1. **模型优化**: 训练完成后导出ONNX格式提高推理速度
2. **参数调整**: 根据香橙派AI pro调整推理参数
3. **结果格式**: 按竞赛要求格式化输出结果
4. **时间优化**: 考虑模型加载时间，可预加载模型

## ❓ 常见问题

**Q: 训练时GPU内存不足怎么办？**
A: 减小batch_size参数，如 `--batch-size 8` 或 `--batch-size 4`

**Q: 如何查看训练进度？**
A: 使用TensorBoard: `tensorboard --logdir runs/train`

**Q: 训练中断了怎么办？**
A: YOLOv11支持自动恢复，重新运行训练脚本即可

**Q: 如何提高模型精度？**
A: 增加训练轮数、调整数据增强参数、或使用更大的模型(yolo11m, yolo11l)

---

🎉 **祝您训练顺利，竞赛取得好成绩！**