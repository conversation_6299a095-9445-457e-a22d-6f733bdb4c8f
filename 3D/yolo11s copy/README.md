# YOLOv11s RGB训练项目

## 项目简介

本项目专为**中国机器人大赛暨RoboCup机器人世界杯中国赛**的**机器人先进视觉赛项3D识别项目**开发。

### 主要特点
- 🎯 **专注RGB识别**: 去除深度数据，仅使用RGB图像进行训练
- 🚀 **YOLOv11s模型**: 使用最新的YOLOv11 Small版本，平衡速度与精度
- 🏆 **竞赛优化**: 针对竞赛场景进行优化配置
- 📊 **完整流水线**: 从数据转换到模型部署的完整解决方案

### 识别类别
根据竞赛规则，支持以下9个类别的物体识别：

| 类别ID | 编号 | 名称 | 类型 |
|--------|------|------|------|
| 0 | CA001 | 衣架 | 日用品 |
| 1 | CA002 | 牙刷 | 日用品 |
| 2 | CB001 | 果冻 | 副食品 |
| 3 | CB002 | 长方形状饼干 | 副食品 |
| 4 | CC001 | 罐装饮料 | 饮料 |
| 5 | CC002 | 瓶装饮料 | 饮料 |
| 6 | CD001 | 香蕉 | 水果 |
| 7 | CD002 | 橙子 | 水果 |
| 8 | Wxxx | 未知物品 | 未知类 |

## 项目结构

```
yolo11s/
├── README.md                 # 项目说明文档
├── run_training.py          # 主训练脚本（一键运行）
├── requirements.txt         # Python依赖包
├── dataset/                 # 数据集目录
│   ├── images/             # RGB图像
│   │   ├── train/          # 训练集图像
│   │   ├── val/            # 验证集图像
│   │   └── test/           # 测试集图像
│   ├── labels/             # YOLO格式标注
│   │   ├── train/          # 训练集标注
│   │   ├── val/            # 验证集标注
│   │   └── test/           # 测试集标注
│   └── dataset.yaml        # 数据集配置文件
├── scripts/                # 脚本目录
│   ├── setup_environment.py    # 环境安装脚本
│   ├── convert_coco_to_yolo.py # 数据格式转换脚本
│   ├── train_yolo11s.py        # 训练脚本
│   └── inference.py            # 推理脚本
└── runs/                   # 训练结果目录
    ├── train/              # 训练结果
    ├── val/                # 验证结果
    └── inference/          # 推理结果
```

## 快速开始

### 1. 环境准备

首先激活conda环境：
```bash
source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet
```

安装依赖包：
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo11s
python scripts/setup_environment.py
```

### 2. 一键训练（推荐）

使用主脚本一键完成所有步骤：
```bash
python run_training.py
```

自定义训练参数：
```bash
python run_training.py --epochs 200 --batch-size 32 --device 0
```

### 3. 分步执行

如果需要分步执行，可以按以下顺序：

#### 步骤1: 数据转换
```bash
python scripts/convert_coco_to_yolo.py
```

#### 步骤2: 模型训练
```bash
python scripts/train_yolo11s.py --data dataset/dataset.yaml --epochs 100
```

#### 步骤3: 模型验证
```bash
python scripts/train_yolo11s.py --validate --model runs/train/yolo11s_rgb/weights/best.pt --data dataset/dataset.yaml
```

#### 步骤4: 推理测试
```bash
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --source dataset/images/test
```

## 使用说明

### 训练参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--epochs` | 100 | 训练轮数 |
| `--batch-size` | 16 | 批次大小 |
| `--img-size` | 640 | 输入图像尺寸 |
| `--device` | auto | 训练设备 (auto/cpu/0/1...) |
| `--workers` | 8 | 数据加载线程数 |

### 推理使用

#### 单张图像推理
```bash
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --source test_image.jpg
```

#### 批量图像推理
```bash
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --source test_images_folder/ --output results/
```

#### 实时摄像头推理
```bash
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --camera --source 0
```

### 竞赛部署

对于竞赛环境部署，建议：

1. **模型导出**: 将训练好的模型导出为ONNX格式以提高推理速度
2. **参数优化**: 根据香橙派AI pro硬件调整推理参数
3. **结果格式**: 按照竞赛要求格式化输出结果

## 训练配置

### 数据增强策略
- 水平翻转: 50%概率
- 色调调整: ±1.5%
- 饱和度调整: ±70%
- 明度调整: ±40%
- Mosaic增强: 100%概率
- 自动增强: RandAugment

### 优化器设置
- 优化器: AdamW
- 初始学习率: 0.01
- 权重衰减: 0.0005
- 预热轮数: 3

### 损失函数权重
- 边界框损失: 7.5
- 分类损失: 0.5
- DFL损失: 1.5

## 性能指标

训练完成后，可以在以下位置查看性能指标：

- **训练日志**: `runs/train/yolo11s_rgb/`
- **验证结果**: `runs/val/`
- **TensorBoard**: `tensorboard --logdir runs/train`

主要关注指标：
- **mAP50**: 在IoU=0.5时的平均精度
- **mAP50-95**: 在IoU=0.5-0.95时的平均精度
- **推理速度**: 单张图像推理时间
- **模型大小**: 模型文件大小

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减小batch_size参数
   - 减小img_size参数
   - 使用CPU训练（添加 `--device cpu`）

2. **数据集路径错误**
   - 检查dataset.yaml中的路径设置
   - 确保图像和标注文件存在

3. **依赖包缺失**
   - 重新运行 `python scripts/setup_environment.py`
   - 手动安装缺失的包

4. **训练中断**
   - 使用 `--resume` 参数继续训练
   - 检查磁盘空间是否充足

### 性能优化建议

1. **硬件优化**
   - 使用GPU训练（推荐RTX 3080或更高）
   - 增加系统内存
   - 使用SSD存储数据集

2. **参数调优**
   - 根据GPU内存调整batch_size
   - 使用混合精度训练（自动启用）
   - 启用图像缓存（cache=True）

3. **数据优化**
   - 预处理图像到统一尺寸
   - 使用多线程数据加载
   - 平衡各类别样本数量

## 竞赛适配

### 硬件要求
- **计算平台**: 香橙派OrangePi AIpro（8T算力，16G内存）
- **摄像头**: 奥比中光Astra Pro Plus（0.6-8m）
- **操作系统**: 不限

### 软件要求
- **识别脚本**: 桌面放置"识别.sh"脚本
- **结果输出**: 按指定格式保存到result_r文件夹
- **通讯协议**: 支持与裁判盒的网络通讯

### 评分标准
- **识别准度分**: 物品ID和数量准确性
- **识别时间分**: 模型加载和推理时间
- **总分计算**: 准度分 + 时间分

## 更新日志

### v1.0.0 (2025-01-13)
- ✅ 初始版本发布
- ✅ 支持COCO到YOLO格式转换
- ✅ YOLOv11s模型训练
- ✅ 完整的推理流水线
- ✅ 竞赛场景优化

## 许可证

本项目仅用于学术研究和竞赛目的。

## 联系方式

如有问题或建议，请联系项目维护者。

---

**祝您在机器人视觉竞赛中取得优异成绩！** 🏆