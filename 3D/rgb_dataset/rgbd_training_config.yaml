# RGB-D融合训练数据集配置文件
# 合并了competition_2025_dataset和reorganized_dataset
# 确保所有RGB图像都有对应的深度图像

# 数据集基本信息
path: /home/<USER>/claude/SpatialVLA/3D/merged_rgbd_dataset
name: "Merged RGB-D Dataset"
version: "1.0"
created: "2025-01-12"

# 数据路径配置
train: images/train
val: images/val
test: images/test

# 深度图像路径配置
depth_train: depth/train
depth_val: depth/val
depth_test: depth/test

# 标注文件路径
annotations:
  train: labels/train/annotations.json
  val: labels/val/annotations.json  # 可根据需要从train中分割
  test: labels/test/annotations.json  # 可根据需要从train中分割
  format: "coco"

# 类别配置
nc: 9  # 类别数量
names: ['CA001_衣架', 'CA002_牙刷', 'CB001_果冻', 'CB002_长方形状饼干', 'CC001_罐装饮料', 'CC002_瓶装饮料', 'CD001_香蕉', 'CD002_橙子', 'Wxxx_未知物品']

# 数据集统计
dataset_stats:
  total_images: 1711
  total_annotations: 11792
  rgb_depth_pairs: 1711
  pairing_rate: 100.0
  
  # 数据分布
  train_images: 1419
  val_images: 194
  test_images: 98
  
  # 数据来源
  competition_dataset: 971
  reorganized_dataset: 740

# RGB-D融合训练配置
rgbd_config:
  # 输入配置
  rgb_channels: 3
  depth_channels: 1
  input_size: [640, 640]  # 可根据模型需求调整
  
  # 深度图像预处理
  depth_preprocessing:
    normalize: true
    depth_range: [0, 10000]  # 毫米单位
    fill_missing: true
    
  # 数据增强（RGB和深度需要保持一致）
  augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.0
    rotation: 10  # 度数
    scale: [0.8, 1.2]
    translation: 0.1
    brightness: 0.2  # 仅RGB
    contrast: 0.2    # 仅RGB
    
  # 融合策略
  fusion_strategy: "early"  # early, late, or attention
  
# 训练建议配置
training_recommendations:
  batch_size: 16  # 根据GPU内存调整
  learning_rate: 0.001
  epochs: 100
  optimizer: "AdamW"
  scheduler: "CosineAnnealingLR"
  
  # 损失函数权重
  loss_weights:
    classification: 1.0
    bbox_regression: 1.0
    depth_consistency: 0.1  # 如果使用深度一致性损失
    
  # 验证配置
  validation:
    interval: 5  # 每5个epoch验证一次
    metrics: ["mAP", "mAP50", "mAP75"]
    
# 模型架构建议
model_suggestions:
  backbone: "EfficientNet-B3"  # 或 ResNet50, YOLOv8等
  neck: "FPN"
  head: "RGB-D Detection Head"
  
  # RGB-D特定配置
  rgb_branch: "standard"
  depth_branch: "lightweight"  # 深度分支可以更轻量
  fusion_module: "attention"   # 注意力机制融合
  
# 数据加载配置
dataloader:
  num_workers: 4
  pin_memory: true
  shuffle: true
  drop_last: false
  
  # RGB-D特定配置
  rgb_mean: [0.485, 0.456, 0.406]
  rgb_std: [0.229, 0.224, 0.225]
  depth_mean: [0.5]  # 归一化后的深度均值
  depth_std: [0.5]   # 归一化后的深度标准差

# 评估配置
evaluation:
  metrics: ["precision", "recall", "mAP", "F1"]
  iou_threshold: 0.5
  confidence_threshold: 0.25
  nms_threshold: 0.45
  
  # RGB-D特定评估
  evaluate_rgb_only: true   # 同时评估仅RGB的性能
  evaluate_depth_only: true # 同时评估仅深度的性能
  evaluate_fusion: true     # 评估融合后的性能

# 文件路径模板
path_templates:
  rgb_image: "{split}/{prefix}_{id}.jpg"
  depth_image: "{split}/{prefix}_{id}_depth.png"
  annotation: "{split}/annotations.json"

# 兼容性信息
compatibility:
  frameworks: ["PyTorch", "TensorFlow", "YOLOv8", "EfficientDet"]
  data_formats: ["COCO", "YOLO"]
  fusion_methods: ["early_fusion", "late_fusion", "attention_fusion"]

# 使用说明
usage_notes: |
  1. 数据集已完全配对，每个RGB图像都有对应的深度图像
  2. 标注采用COCO格式，包含边界框和类别信息
  3. 建议使用early fusion或attention fusion进行RGB-D融合
  4. 深度图像已预处理，范围在0-10000mm
  5. 可根据需要将train数据分割为train/val
  6. 支持多种深度学习框架和模型架构

# 质量保证
quality_assurance:
  rgb_depth_pairing: "100% paired"
  annotation_coverage: "100% annotated"
  data_validation: "passed"
  training_readiness: "ready"
  
# 联系信息
contact:
  created_by: "Data Processing Pipeline"
  validation_date: "2025-01-12"
  status: "Production Ready"
