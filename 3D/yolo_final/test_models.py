#!/usr/bin/env python3
"""
测试YOLO模型可用性
验证YOLO11和YOLO12模型是否可以正常加载
"""

import sys
from pathlib import Path

def test_yolo_models():
    """测试所有YOLO模型的可用性"""
    try:
        from ultralytics import YOLO
        import ultralytics
        print(f"Ultralytics版本: {ultralytics.__version__}")
    except ImportError:
        print("错误: ultralytics包未安装")
        print("请运行: pip install ultralytics")
        return False
    
    # 要测试的模型列表
    models_to_test = [
        'yolo11n', 'yolo11s', 'yolo11m',
        'yolo12n', 'yolo12s', 'yolo12m'
    ]
    
    print("\n测试模型可用性:")
    print("="*50)
    
    available_models = []
    failed_models = []
    
    for model_name in models_to_test:
        try:
            print(f"测试 {model_name}...", end=" ")
            
            # 尝试创建模型对象
            model = YOLO(f'{model_name}.pt')
            
            # 如果成功，模型会自动下载（如果需要）
            print("✓ 可用")
            available_models.append(model_name)
            
        except Exception as e:
            print(f"✗ 失败: {str(e)[:50]}...")
            failed_models.append(model_name)
    
    print("\n" + "="*50)
    print("测试结果:")
    print(f"✓ 可用模型 ({len(available_models)}): {', '.join(available_models)}")
    if failed_models:
        print(f"✗ 失败模型 ({len(failed_models)}): {', '.join(failed_models)}")
    
    print(f"\n总计: {len(available_models)}/{len(models_to_test)} 个模型可用")
    
    if len(available_models) >= 3:
        print("✓ 足够的模型可用于训练")
        return True
    else:
        print("✗ 可用模型不足，建议检查网络连接或ultralytics版本")
        return False

def test_gpu_availability():
    """测试GPU可用性"""
    try:
        import torch
        print(f"\nGPU测试:")
        print("="*30)
        print(f"CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"GPU数量: {gpu_count}")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"GPU {i}: {gpu_name}")
            
            # 检查目标GPU
            target_gpus = [4, 5]
            available_target_gpus = [gpu for gpu in target_gpus if gpu < gpu_count]
            
            if available_target_gpus:
                print(f"✓ 目标GPU可用: {available_target_gpus}")
                return True
            else:
                print(f"✗ 目标GPU {target_gpus} 不可用")
                print(f"可用GPU范围: 0-{gpu_count-1}")
                return False
        else:
            print("✗ CUDA不可用")
            return False
            
    except ImportError:
        print("✗ PyTorch未安装")
        return False

def main():
    """主测试函数"""
    print("YOLO多模型训练系统 - 环境测试")
    print("="*60)
    
    # 测试模型可用性
    models_ok = test_yolo_models()
    
    # 测试GPU可用性
    gpu_ok = test_gpu_availability()
    
    print("\n" + "="*60)
    print("测试总结:")
    
    if models_ok and gpu_ok:
        print("✓ 所有测试通过，可以开始训练")
        print("\n启动训练:")
        print("python run.py")
    else:
        print("✗ 部分测试失败，请解决问题后重试")
        
        if not models_ok:
            print("  - 检查网络连接和ultralytics版本")
        if not gpu_ok:
            print("  - 检查CUDA安装和GPU可用性")

if __name__ == "__main__":
    main()
