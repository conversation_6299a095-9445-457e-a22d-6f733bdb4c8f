#!/usr/bin/env python3
"""
一键启动多模型YOLO训练
支持在指定GPU(4,5)上训练yolo11n、yolo11s、yolo11m、yolo12n、yolo12s、yolo12m
"""

import os
import sys
import time
import subprocess
from pathlib import Path
from config import TRAINING_CONFIG, MODEL_CONFIGS, get_model_batch_size

def check_yolo_version():
    """检查YOLO版本和可用模型"""
    try:
        from ultralytics import YOLO
        import ultralytics
        print(f"Ultralytics版本: {ultralytics.__version__}")

        # 支持的模型列表（包括YOLO12）
        all_models = ['yolo11n', 'yolo11s', 'yolo11m', 'yolo12n', 'yolo12s', 'yolo12m']
        available_models = []

        for model_name in all_models:
            try:
                print(f"检查 {model_name} 支持...")
                # 尝试创建模型对象来验证支持
                model = YOLO(f'{model_name}.pt')
                available_models.append(model_name)
                print(f"✓ {model_name} 可用")
            except Exception as e:
                print(f"✗ {model_name} 不可用: {e}")

        return available_models

    except ImportError:
        print("错误: ultralytics包未安装")
        print("请运行: pip install ultralytics")
        return []

def setup_environment():
    """设置训练环境"""
    print("设置训练环境...")
    
    # 创建必要的目录
    base_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final")
    dirs_to_create = [
        base_dir / "dataset",
        base_dir / "results", 
        base_dir / "logs"
    ]
    
    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {dir_path}")
    
    # 检查数据集
    dataset_yaml = base_dir / "dataset" / "dataset.yaml"
    if not dataset_yaml.exists():
        print("数据集不存在，开始转换...")
        try:
            from convert_dataset import convert_coco_to_yolo
            convert_coco_to_yolo()
            print("数据集转换完成")
        except Exception as e:
            print(f"数据集转换失败: {e}")
            return False
    else:
        print("数据集已存在")
    
    return True

def run_training():
    """运行训练"""
    print("="*60)
    print("多模型YOLO训练系统")
    print("="*60)
    print("数据集: /home/<USER>/claude/SpatialVLA/3D/cocofinalfinal")
    print("目标GPU: 4, 5")
    print("训练模型: yolo11n, yolo11s, yolo11m (YOLO12使用YOLO11替代)")
    print("="*60)
    
    # 检查YOLO版本
    available_models = check_yolo_version()
    if not available_models:
        print("没有可用的YOLO模型")
        return False
    
    print(f"可用模型: {available_models}")
    
    # 设置环境
    if not setup_environment():
        print("环境设置失败")
        return False
    
    # 配置训练参数 - 包含YOLO11和YOLO12模型
    target_models = ['yolo11n', 'yolo11s', 'yolo11m', 'yolo12n', 'yolo12s', 'yolo12m']
    gpus = [4, 5]  # 指定的GPU
    epochs = 100
    
    print(f"\n开始训练配置:")
    print(f"  模型: {target_models}")
    print(f"  GPU: {gpus}")
    print(f"  训练轮数: {epochs}")
    
    # 导入并运行训练器
    try:
        from multi_model_trainer import MultiModelTrainer
        
        # 创建训练器
        trainer = MultiModelTrainer(
            gpus=gpus,
            epochs=epochs,
            batch_size=16
        )
        
        # 检查环境
        if not trainer.check_environment():
            print("训练环境检查失败")
            return False
        
        # 开始训练
        print("\n开始多模型训练...")
        start_time = time.time()
        
        trainer.train_all_models(target_models)
        
        # 计算总时间
        total_time = time.time() - start_time
        hours = int(total_time // 3600)
        minutes = int((total_time % 3600) // 60)
        
        print(f"\n训练完成! 总时间: {hours}小时 {minutes}分钟")
        
        # 显示结果
        print_results_summary()
        
        return True
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_results_summary():
    """打印结果摘要"""
    results_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final/results")
    
    print("\n" + "="*60)
    print("训练结果摘要")
    print("="*60)
    
    if not results_dir.exists():
        print("结果目录不存在")
        return
    
    # 查找训练结果
    for model_dir in sorted(results_dir.iterdir()):
        if model_dir.is_dir():
            model_name = model_dir.name
            print(f"\n模型: {model_name}")
            
            # 检查训练结果
            training_dir = model_dir / f"{model_name}_training"
            if training_dir.exists():
                weights_dir = training_dir / "weights"
                if weights_dir.exists():
                    best_pt = weights_dir / "best.pt"
                    if best_pt.exists():
                        file_size = best_pt.stat().st_size / (1024*1024)  # MB
                        print(f"  ✓ 最佳模型: {best_pt}")
                        print(f"    文件大小: {file_size:.1f} MB")
                    else:
                        print(f"  ✗ 最佳模型不存在")
                else:
                    print(f"  ✗ weights目录不存在")
            else:
                print(f"  ✗ 训练目录不存在")
            
            # 检查结果JSON
            results_json = model_dir / f"{model_name}_results.json"
            if results_json.exists():
                try:
                    import json
                    with open(results_json, 'r') as f:
                        results = json.load(f)
                    
                    if results.get('training_completed', False):
                        print(f"  ✓ mAP50: {results.get('map50', 0):.4f}")
                        print(f"  ✓ mAP50-95: {results.get('map50_95', 0):.4f}")
                        print(f"  ✓ 训练时间: {results.get('training_time', 0)/60:.1f} 分钟")
                    else:
                        print(f"  ✗ 训练未完成")
                        if 'error' in results:
                            print(f"    错误: {results['error']}")
                except Exception as e:
                    print(f"  ✗ 无法读取结果: {e}")
            else:
                print(f"  ✗ 结果文件不存在")
    
    print(f"\n详细结果保存在: {results_dir}")
    print(f"日志文件保存在: /home/<USER>/claude/SpatialVLA/3D/yolo_final/logs/")

def main():
    """主函数"""
    print("启动多模型YOLO训练系统...")
    
    try:
        success = run_training()
        if success:
            print("\n🎉 所有训练任务完成!")
        else:
            print("\n❌ 训练过程中出现问题")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n系统错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
