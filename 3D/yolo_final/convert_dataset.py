#!/usr/bin/env python3
"""
数据集转换脚本 - 将COCO格式转换为YOLO格式
专用于多模型训练系统
"""

import json
import os
import shutil
import random
from pathlib import Path
from tqdm import tqdm
import yaml

def convert_bbox_coco_to_yolo(bbox, img_width, img_height):
    """将COCO格式的bbox转换为YOLO格式"""
    x_min, y_min, width, height = bbox
    
    # 转换为中心点坐标
    x_center = x_min + width / 2
    y_center = y_min + height / 2
    
    # 归一化到0-1
    x_center_norm = x_center / img_width
    y_center_norm = y_center / img_height
    width_norm = width / img_width
    height_norm = height / img_height
    
    return [x_center_norm, y_center_norm, width_norm, height_norm]

def convert_coco_to_yolo():
    """转换COCO数据集为YOLO格式"""
    print("开始转换数据集...")
    
    # 路径设置
    source_dir = Path("/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal")
    target_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final/dataset")
    
    source_images_dir = source_dir / "images"
    source_labels_file = source_dir / "_annotations.coco.json"
    
    # 检查源文件
    if not source_labels_file.exists():
        raise FileNotFoundError(f"标注文件不存在: {source_labels_file}")
    if not source_images_dir.exists():
        raise FileNotFoundError(f"图像目录不存在: {source_images_dir}")
    
    # 加载COCO标注
    with open(source_labels_file, 'r', encoding='utf-8') as f:
        coco_data = json.load(f)
    
    # 创建类别映射
    name_mapping = {
        'CA001': 'CA001_衣架',
        'CA002': 'CA002_牙刷', 
        'CB001': 'CB001_果冻',
        'CB002': 'CB002_长方形状饼干',
        'CC001': 'CC001_罐装饮料',
        'CC002': 'CC002_瓶装饮料',
        'CD001': 'CD001_香蕉',
        'CD002': 'CD002_橙子',
        'Wxxx': 'Wxxx_未知物品'
    }
    
    category_mapping = {}
    class_names = []
    for cat in coco_data['categories']:
        cat_name = cat['name']
        full_name = name_mapping.get(cat_name, cat_name)
        new_idx = len(class_names)
        category_mapping[cat['id']] = new_idx
        class_names.append(full_name)
    
    print(f"检测到 {len(class_names)} 个类别: {class_names}")
    
    # 创建图像映射和标注分组
    image_id_to_info = {img['id']: img for img in coco_data['images']}
    image_annotations = {}
    for ann in coco_data['annotations']:
        image_id = ann['image_id']
        if image_id not in image_annotations:
            image_annotations[image_id] = []
        image_annotations[image_id].append(ann)
    
    # 随机分割数据集
    random.seed(42)
    all_image_ids = list(image_id_to_info.keys())
    random.shuffle(all_image_ids)
    
    total_images = len(all_image_ids)
    train_count = int(total_images * 0.8)
    val_count = int(total_images * 0.1)
    
    splits = {
        'train': all_image_ids[:train_count],
        'val': all_image_ids[train_count:train_count + val_count],
        'test': all_image_ids[train_count + val_count:]
    }
    
    print(f"数据分割: 训练集{len(splits['train'])}, 验证集{len(splits['val'])}, 测试集{len(splits['test'])}")
    
    # 创建目标目录
    for split_name in ['train', 'val', 'test']:
        (target_dir / "images" / split_name).mkdir(parents=True, exist_ok=True)
        (target_dir / "labels" / split_name).mkdir(parents=True, exist_ok=True)
    
    # 处理每个分割
    total_processed = 0
    for split_name, image_ids in splits.items():
        if not image_ids:
            continue
            
        print(f"处理 {split_name} 数据集 ({len(image_ids)} 张图像)...")
        target_images_dir = target_dir / "images" / split_name
        target_labels_dir = target_dir / "labels" / split_name
        
        for image_id in tqdm(image_ids, desc=f"转换{split_name}"):
            image_info = image_id_to_info[image_id]
            image_filename = image_info['file_name']
            img_width = image_info['width']
            img_height = image_info['height']
            
            # 复制图像文件
            source_image_path = source_images_dir / image_filename
            target_image_path = target_images_dir / image_filename
            
            if source_image_path.exists():
                shutil.copy2(source_image_path, target_image_path)
            else:
                continue
            
            # 转换标注
            yolo_annotations = []
            if image_id in image_annotations:
                for ann in image_annotations[image_id]:
                    category_id = ann['category_id']
                    bbox = ann['bbox']
                    
                    if category_id in category_mapping:
                        yolo_class_id = category_mapping[category_id]
                        yolo_bbox = convert_bbox_coco_to_yolo(bbox, img_width, img_height)
                        yolo_line = f"{yolo_class_id} {' '.join(map(str, yolo_bbox))}"
                        yolo_annotations.append(yolo_line)
            
            # 保存标注文件
            label_filename = Path(image_filename).stem + '.txt'
            target_label_path = target_labels_dir / label_filename
            
            with open(target_label_path, 'w') as f:
                f.write('\n'.join(yolo_annotations))
            
            total_processed += 1
    
    # 创建数据集配置文件
    dataset_config = {
        'path': str(target_dir.absolute()),
        'train': 'images/train',
        'val': 'images/val',
        'test': 'images/test',
        'nc': len(class_names),
        'names': class_names
    }
    
    yaml_path = target_dir / 'dataset.yaml'
    with open(yaml_path, 'w', encoding='utf-8') as f:
        f.write(f"# Multi-Model YOLO Training Dataset\n")
        f.write(f"path: {dataset_config['path']}\n")
        f.write(f"train: {dataset_config['train']}\n")
        f.write(f"val: {dataset_config['val']}\n")
        f.write(f"test: {dataset_config['test']}\n\n")
        f.write(f"nc: {dataset_config['nc']}\n")
        f.write(f"names: {class_names}\n")
    
    print(f"转换完成! 总计处理: {total_processed} 张图像")
    print(f"数据集配置文件: {yaml_path}")
    return str(yaml_path)

if __name__ == "__main__":
    convert_coco_to_yolo()
