# 多模型YOLO训练系统 - 使用说明

## 🎯 项目完成状态

✅ **已完成的工作**:
- 完整的多模型训练系统已创建在 `/home/<USER>/claude/SpatialVLA/3D/yolo_final`
- 支持在指定GPU(4,5)上并行训练多个YOLO模型
- 自动从 `/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal` 转换数据集
- 每个模型的结果完全分离保存
- 一键启动Python文件已准备就绪

## 📊 训练配置

**数据集**: `/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal` (2025张图像)
**目标GPU**: 4, 5
**训练模型**: yolo11n, yolo11s, yolo11m, yolo12n, yolo12s, yolo12m (6个模型)
**训练轮数**: 100 epochs
**预计时间**: 6-12小时

## 🚀 一键启动训练

### 方法1: 最简单启动（推荐）
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo_final
python run.py
```

### 方法2: 直接启动
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo_final
python start_training.py
```

### 方法3: 自定义参数
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo_final
python multi_model_trainer.py --models yolo11n yolo11s yolo11m --gpus 4 5 --epochs 100
```

## 📁 结果保存结构

训练完成后，结果将按以下结构保存：

```
/home/<USER>/claude/SpatialVLA/3D/yolo_final/
├── results/                     # 训练结果（完全分离）
│   ├── yolo11n/                 # YOLO11n模型结果
│   │   ├── yolo11n_training/
│   │   │   ├── weights/
│   │   │   │   ├── best.pt      # 最佳模型
│   │   │   │   └── last.pt      # 最后模型
│   │   │   ├── train_batch*.jpg # 训练可视化
│   │   │   ├── val_batch*.jpg   # 验证可视化
│   │   │   └── results.png      # 训练曲线
│   │   └── yolo11n_results.json # 性能报告
│   ├── yolo11s/                 # YOLO11s模型结果
│   │   └── ... (同上结构)
│   ├── yolo11m/                 # YOLO11m模型结果
│   │   └── ... (同上结构)
│   ├── yolo12n/                 # YOLO12n模型结果
│   │   └── ... (同上结构)
│   ├── yolo12s/                 # YOLO12s模型结果
│   │   └── ... (同上结构)
│   └── yolo12m/                 # YOLO12m模型结果
│       └── ... (同上结构)
├── logs/                        # 训练日志
│   ├── yolo11n_gpu4.log
│   ├── yolo11s_gpu5.log
│   └── yolo11m_gpu4.log
└── training_summary.json       # 总体训练报告
```

## 📊 训练监控

### 实时监控
```bash
# 查看训练日志
tail -f /home/<USER>/claude/SpatialVLA/3D/yolo_final/logs/yolo11n_gpu4.log

# 监控GPU使用情况
nvidia-smi -l 1
```

### 训练完成后查看结果
```bash
# 查看训练摘要
cat /home/<USER>/claude/SpatialVLA/3D/yolo_final/results/training_summary.json

# 查看各模型性能
ls -la /home/<USER>/claude/SpatialVLA/3D/yolo_final/results/*/
```

## 🎯 预期结果

### 模型性能预期
| 模型 | 预期mAP50 | 预期大小 | 训练时间 |
|------|-----------|----------|----------|
| yolo11n | 0.80-0.85 | ~6MB | 1-2小时 |
| yolo11s | 0.85-0.90 | ~22MB | 2-3小时 |
| yolo11m | 0.88-0.92 | ~50MB | 3-4小时 |
| yolo12n | 0.82-0.87 | ~6MB | 1-2小时 |
| yolo12s | 0.87-0.92 | ~22MB | 2-3小时 |
| yolo12m | 0.90-0.94 | ~50MB | 3-4小时 |

### 训练完成标志
训练成功完成后，您将看到：
```
训练完成! 总时间: X小时 Y分钟

训练结果摘要
============================================
模型        状态    GPU   mAP50    mAP50-95  时间(分钟)
yolo11n     完成    4     0.8500   0.6500    60.0
yolo11s     完成    5     0.8700   0.6800    75.0
yolo11m     完成    4     0.8900   0.7100    90.0
yolo12n     完成    5     0.8600   0.6700    65.0
yolo12s     完成    4     0.8900   0.7000    80.0
yolo12m     完成    5     0.9200   0.7400    95.0

🎉 所有训练任务完成!
```

## 🔍 使用训练好的模型

### 推理示例
```python
from ultralytics import YOLO

# 加载最佳模型
model = YOLO('/home/<USER>/claude/SpatialVLA/3D/yolo_final/results/yolo11s/yolo11s_training/weights/best.pt')

# 单张图像推理
results = model.predict('test_image.jpg')

# 批量推理
results = model.predict('test_images_folder/')

# 实时摄像头推理
results = model.predict(source=0)
```

### 模型对比
```python
# 加载所有训练好的模型进行对比
models = {
    'yolo11n': YOLO('/home/<USER>/claude/SpatialVLA/3D/yolo_final/results/yolo11n/yolo11n_training/weights/best.pt'),
    'yolo11s': YOLO('/home/<USER>/claude/SpatialVLA/3D/yolo_final/results/yolo11s/yolo11s_training/weights/best.pt'),
    'yolo11m': YOLO('/home/<USER>/claude/SpatialVLA/3D/yolo_final/results/yolo11m/yolo11m_training/weights/best.pt')
}

# 在测试集上验证所有模型
for name, model in models.items():
    results = model.val(data='/home/<USER>/claude/SpatialVLA/3D/yolo_final/dataset/dataset.yaml')
    print(f"{name}: mAP50={results.box.map50:.4f}")
```

## ⚠️ 注意事项

### YOLO12模型说明
- 由于YOLO12可能不在当前ultralytics版本中，系统会自动使用YOLO11模型替代
- 如果需要真正的YOLO12模型，请更新ultralytics到最新版本

### GPU内存管理
- 如果遇到GPU内存不足，系统会自动调整批次大小
- 可以手动指定更小的批次大小: `--batch-size 8`

### 训练中断恢复
- 如果训练中断，可以重新运行脚本
- YOLO会自动从最后的检查点恢复训练

## 🛠️ 故障排除

### 常见问题
1. **GPU不可用**: 检查CUDA安装和GPU驱动
2. **内存不足**: 减小批次大小或使用更小的模型
3. **数据集转换失败**: 检查源数据集路径是否正确
4. **模型下载失败**: 检查网络连接，可能需要手动下载

### 获取帮助
```bash
# 查看详细帮助
python multi_model_trainer.py --help

# 检查系统状态
python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}, GPUs: {torch.cuda.device_count()}')"
```

---

## 🎉 开始训练

一切准备就绪！现在您可以：

1. **立即开始**: `cd /home/<USER>/claude/SpatialVLA/3D/yolo_final && python run.py`
2. **监控进度**: 查看日志文件和GPU使用情况
3. **等待完成**: 3-6小时后获得3个完整训练的YOLO模型
4. **使用模型**: 用于推理、验证或进一步优化

**祝您训练顺利，取得优异成绩！** 🚀🤖
