#!/usr/bin/env python3
"""
多模型YOLO训练器
支持在指定GPU上训练多个YOLO模型并分开保存结果
"""

import os
import sys
import time
import json
import threading
import subprocess
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import torch
from ultralytics import YOLO

class MultiModelTrainer:
    def __init__(self, gpus=[4, 5], epochs=100, batch_size=16):
        self.gpus = gpus
        self.epochs = epochs
        self.batch_size = batch_size
        self.base_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final")
        self.dataset_yaml = self.base_dir / "dataset" / "dataset.yaml"
        self.results_dir = self.base_dir / "results"
        self.logs_dir = self.base_dir / "logs"
        
        # 支持的模型列表（包括YOLO11和YOLO12）
        self.models = [
            'yolo11n', 'yolo11s', 'yolo11m', 'yolo11l', 'yolo11x',
            'yolo12n', 'yolo12s', 'yolo12m', 'yolo12l', 'yolo12x'
        ]
        
        # 创建结果目录
        self.results_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # 训练状态跟踪
        self.training_status = {}
        self.training_results = {}
        
    def check_environment(self):
        """检查训练环境"""
        print("检查训练环境...")
        
        # 检查CUDA
        if not torch.cuda.is_available():
            print("错误: CUDA不可用")
            return False
        
        print(f"可用GPU数量: {torch.cuda.device_count()}")
        for gpu in self.gpus:
            if gpu >= torch.cuda.device_count():
                print(f"错误: GPU {gpu} 不存在")
                return False
            print(f"GPU {gpu}: {torch.cuda.get_device_name(gpu)}")
        
        # 检查数据集
        if not self.dataset_yaml.exists():
            print("数据集不存在，开始转换...")
            from convert_dataset import convert_coco_to_yolo
            convert_coco_to_yolo()
        
        if not self.dataset_yaml.exists():
            print("错误: 数据集转换失败")
            return False
        
        print("环境检查完成")
        return True
    
    def train_single_model(self, model_name, gpu_id):
        """训练单个模型"""
        start_time = time.time()
        
        try:
            print(f"开始训练 {model_name} 在 GPU {gpu_id}")
            
            # 设置结果目录
            model_results_dir = self.results_dir / model_name
            model_results_dir.mkdir(exist_ok=True)
            
            # 创建日志文件
            log_file = self.logs_dir / f"{model_name}_gpu{gpu_id}.log"
            
            # 加载模型
            model = YOLO(f'{model_name}.pt')
            
            # 训练参数
            train_args = {
                'data': str(self.dataset_yaml),
                'epochs': self.epochs,
                'batch': self.batch_size,
                'device': gpu_id,
                'project': str(model_results_dir),
                'name': f'{model_name}_training',
                'save': True,
                'cache': True,
                'optimizer': 'AdamW',
                'lr0': 0.01,
                'lrf': 0.01,
                'momentum': 0.937,
                'weight_decay': 0.0005,
                'warmup_epochs': 3,
                'box': 7.5,
                'cls': 0.5,
                'dfl': 1.5,
                'hsv_h': 0.015,
                'hsv_s': 0.7,
                'hsv_v': 0.4,
                'degrees': 0.0,
                'translate': 0.1,
                'scale': 0.5,
                'shear': 0.0,
                'perspective': 0.0,
                'flipud': 0.0,
                'fliplr': 0.5,
                'mosaic': 1.0,
                'mixup': 0.0,
                'copy_paste': 0.0,
                'verbose': False  # 减少输出
            }
            
            # 开始训练
            self.training_status[model_name] = "训练中"
            results = model.train(**train_args)
            
            # 验证模型
            best_model_path = model_results_dir / f'{model_name}_training' / 'weights' / 'best.pt'
            if best_model_path.exists():
                print(f"验证 {model_name}...")
                model = YOLO(str(best_model_path))
                val_results = model.val(data=str(self.dataset_yaml), verbose=False)
                
                # 保存验证结果
                results_summary = {
                    'model': model_name,
                    'gpu': gpu_id,
                    'training_time': time.time() - start_time,
                    'best_model_path': str(best_model_path),
                    'map50': float(val_results.box.map50) if val_results.box.map50 is not None else 0.0,
                    'map50_95': float(val_results.box.map) if val_results.box.map is not None else 0.0,
                    'training_completed': True
                }
                
                # 保存结果到JSON
                results_file = model_results_dir / f'{model_name}_results.json'
                with open(results_file, 'w', encoding='utf-8') as f:
                    json.dump(results_summary, f, ensure_ascii=False, indent=2)
                
                self.training_results[model_name] = results_summary
                self.training_status[model_name] = "完成"
                
                print(f"✓ {model_name} 训练完成 - mAP50: {results_summary['map50']:.4f}")
                
            return results_summary
            
        except Exception as e:
            error_msg = f"训练失败: {str(e)}"
            print(f"✗ {model_name} {error_msg}")
            
            self.training_status[model_name] = f"失败: {error_msg}"
            
            error_summary = {
                'model': model_name,
                'gpu': gpu_id,
                'training_time': time.time() - start_time,
                'error': error_msg,
                'training_completed': False
            }
            
            self.training_results[model_name] = error_summary
            return error_summary
    
    def train_all_models(self, selected_models=None):
        """训练所有模型"""
        if selected_models is None:
            selected_models = self.models
        
        print(f"开始训练 {len(selected_models)} 个模型: {selected_models}")
        print(f"使用GPU: {self.gpus}")
        print(f"训练参数: epochs={self.epochs}, batch_size={self.batch_size}")
        
        # 创建训练任务
        training_tasks = []
        gpu_index = 0
        
        for model_name in selected_models:
            gpu_id = self.gpus[gpu_index % len(self.gpus)]
            training_tasks.append((model_name, gpu_id))
            gpu_index += 1
        
        # 并行训练
        max_workers = len(self.gpus)  # 每个GPU一个worker
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交训练任务
            future_to_model = {
                executor.submit(self.train_single_model, model_name, gpu_id): model_name
                for model_name, gpu_id in training_tasks
            }
            
            # 等待完成
            for future in as_completed(future_to_model):
                model_name = future_to_model[future]
                try:
                    result = future.result()
                    print(f"模型 {model_name} 训练任务完成")
                except Exception as exc:
                    print(f"模型 {model_name} 训练异常: {exc}")
        
        # 生成总结报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """生成训练总结报告"""
        print("\n" + "="*60)
        print("训练总结报告")
        print("="*60)
        
        summary_data = {
            'training_date': datetime.now().isoformat(),
            'gpus_used': self.gpus,
            'epochs': self.epochs,
            'batch_size': self.batch_size,
            'models_trained': len(self.training_results),
            'results': self.training_results
        }
        
        # 保存总结报告
        summary_file = self.results_dir / 'training_summary.json'
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        # 打印结果表格
        print(f"{'模型':<12} {'状态':<8} {'GPU':<4} {'mAP50':<8} {'mAP50-95':<10} {'时间(分钟)':<10}")
        print("-" * 60)
        
        for model_name, result in self.training_results.items():
            status = "完成" if result.get('training_completed', False) else "失败"
            gpu = result.get('gpu', 'N/A')
            map50 = f"{result.get('map50', 0):.4f}" if result.get('training_completed', False) else "N/A"
            map50_95 = f"{result.get('map50_95', 0):.4f}" if result.get('training_completed', False) else "N/A"
            time_min = f"{result.get('training_time', 0)/60:.1f}" if 'training_time' in result else "N/A"
            
            print(f"{model_name:<12} {status:<8} {gpu:<4} {map50:<8} {map50_95:<10} {time_min:<10}")
        
        print(f"\n总结报告已保存: {summary_file}")
        print(f"详细结果目录: {self.results_dir}")
        
        # 显示最佳模型
        completed_models = [r for r in self.training_results.values() if r.get('training_completed', False)]
        if completed_models:
            best_model = max(completed_models, key=lambda x: x.get('map50', 0))
            print(f"\n最佳模型: {best_model['model']} (mAP50: {best_model['map50']:.4f})")
            print(f"模型路径: {best_model['best_model_path']}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='多模型YOLO训练器')
    parser.add_argument('--gpus', nargs='+', type=int, default=[4, 5], help='使用的GPU ID')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--models', nargs='+',
                       choices=['yolo11n', 'yolo11s', 'yolo11m', 'yolo11l', 'yolo11x',
                               'yolo12n', 'yolo12s', 'yolo12m', 'yolo12l', 'yolo12x'],
                       default=['yolo11n', 'yolo11s', 'yolo11m', 'yolo12n', 'yolo12s', 'yolo12m'],
                       help='要训练的模型 (支持YOLO11和YOLO12)')
    
    args = parser.parse_args()
    
    print("多模型YOLO训练系统")
    print("="*50)
    
    # 创建训练器
    trainer = MultiModelTrainer(
        gpus=args.gpus,
        epochs=args.epochs,
        batch_size=args.batch_size
    )
    
    # 检查环境
    if not trainer.check_environment():
        sys.exit(1)
    
    # 开始训练
    trainer.train_all_models(args.models)
    
    print("\n所有训练任务完成!")

if __name__ == "__main__":
    main()
