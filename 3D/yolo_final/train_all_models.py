#!/usr/bin/env python3
"""
一键启动多模型YOLO训练
支持yolo11n、yolo11s、yolo11m在指定GPU(4,5)上训练
"""

import os
import sys
import time
from pathlib import Path
from multi_model_trainer import MultiModelTrainer

def main():
    """一键启动训练"""
    print("="*60)
    print("多模型YOLO训练系统 - 一键启动")
    print("="*60)
    print("数据集: /home/<USER>/claude/SpatialVLA/3D/cocofinalfinal")
    print("目标GPU: 4, 5")
    print("训练模型: yolo11n, yolo11s, yolo11m")
    print("="*60)
    
    # 配置参数
    config = {
        'gpus': [4, 5],  # 指定GPU 4和5
        'epochs': 100,   # 训练轮数
        'batch_size': 16,  # 批次大小
        'models': ['yolo11n', 'yolo11s', 'yolo11m']  # 要训练的模型
    }
    
    print(f"训练配置:")
    print(f"  GPU: {config['gpus']}")
    print(f"  训练轮数: {config['epochs']}")
    print(f"  批次大小: {config['batch_size']}")
    print(f"  模型列表: {config['models']}")
    print()
    
    # 创建训练器
    trainer = MultiModelTrainer(
        gpus=config['gpus'],
        epochs=config['epochs'],
        batch_size=config['batch_size']
    )
    
    # 检查环境
    print("检查训练环境...")
    if not trainer.check_environment():
        print("环境检查失败，退出训练")
        sys.exit(1)
    
    print("环境检查通过，开始训练...")
    print()
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 开始训练所有模型
        trainer.train_all_models(config['models'])
        
        # 计算总训练时间
        total_time = time.time() - start_time
        hours = int(total_time // 3600)
        minutes = int((total_time % 3600) // 60)
        
        print()
        print("="*60)
        print("训练完成!")
        print("="*60)
        print(f"总训练时间: {hours}小时 {minutes}分钟")
        print(f"结果保存目录: /home/<USER>/claude/SpatialVLA/3D/yolo_final/results/")
        print(f"日志保存目录: /home/<USER>/claude/SpatialVLA/3D/yolo_final/logs/")
        
        # 显示结果目录结构
        results_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final/results")
        if results_dir.exists():
            print("\n训练结果目录结构:")
            for model_dir in sorted(results_dir.iterdir()):
                if model_dir.is_dir():
                    print(f"  {model_dir.name}/")
                    training_dir = model_dir / f"{model_dir.name}_training"
                    if training_dir.exists():
                        weights_dir = training_dir / "weights"
                        if weights_dir.exists():
                            best_pt = weights_dir / "best.pt"
                            last_pt = weights_dir / "last.pt"
                            if best_pt.exists():
                                print(f"    ├── {model_dir.name}_training/weights/best.pt")
                            if last_pt.exists():
                                print(f"    ├── {model_dir.name}_training/weights/last.pt")
                    
                    results_json = model_dir / f"{model_dir.name}_results.json"
                    if results_json.exists():
                        print(f"    └── {model_dir.name}_results.json")
        
        print("\n推理使用示例:")
        for model in config['models']:
            model_path = f"/home/<USER>/claude/SpatialVLA/3D/yolo_final/results/{model}/{model}_training/weights/best.pt"
            print(f"  {model}: python -c \"from ultralytics import YOLO; YOLO('{model_path}').predict('image.jpg')\"")
        
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n训练过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
