#!/usr/bin/env python3
"""
移除YOLO数据集中的"Wxxx"类别
从COCO格式的标注文件中完全移除指定类别的所有数据
"""

import json
import os
import shutil
from datetime import datetime
from collections import defaultdict

def backup_file(file_path):
    """创建文件备份"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    shutil.copy2(file_path, backup_path)
    print(f"✓ 已创建备份: {backup_path}")
    return backup_path

def load_coco_data(file_path):
    """加载COCO格式的JSON数据"""
    print(f"📖 正在加载数据集: {file_path}")
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"✓ 数据加载完成")
    print(f"  - 图片数量: {len(data.get('images', []))}")
    print(f"  - 标注数量: {len(data.get('annotations', []))}")
    print(f"  - 类别数量: {len(data.get('categories', []))}")
    
    return data

def analyze_category_to_remove(data, category_name="Wxxx"):
    """分析要移除的类别"""
    print(f"\n🔍 分析要移除的类别: {category_name}")
    
    # 找到目标类别
    target_category = None
    for cat in data.get('categories', []):
        if cat['name'] == category_name:
            target_category = cat
            break
    
    if not target_category:
        print(f"❌ 未找到类别: {category_name}")
        return None, {}
    
    category_id = target_category['id']
    print(f"✓ 找到目标类别: ID={category_id}, Name={category_name}")
    
    # 统计相关标注
    annotations_to_remove = []
    affected_images = set()
    
    for ann in data.get('annotations', []):
        if ann['category_id'] == category_id:
            annotations_to_remove.append(ann['id'])
            affected_images.add(ann['image_id'])
    
    stats = {
        'category_id': category_id,
        'category_name': category_name,
        'annotations_count': len(annotations_to_remove),
        'affected_images_count': len(affected_images),
        'annotation_ids': annotations_to_remove,
        'affected_image_ids': list(affected_images)
    }
    
    print(f"  - 要删除的标注数量: {stats['annotations_count']}")
    print(f"  - 受影响的图片数量: {stats['affected_images_count']}")
    
    return target_category, stats

def remove_category_data(data, stats):
    """移除类别相关的所有数据"""
    print(f"\n🗑️  开始移除类别数据...")
    
    category_id = stats['category_id']
    
    # 移除标注
    original_ann_count = len(data['annotations'])
    data['annotations'] = [ann for ann in data['annotations'] 
                          if ann['category_id'] != category_id]
    removed_ann_count = original_ann_count - len(data['annotations'])
    
    # 移除类别定义
    original_cat_count = len(data['categories'])
    data['categories'] = [cat for cat in data['categories'] 
                         if cat['id'] != category_id]
    removed_cat_count = original_cat_count - len(data['categories'])
    
    print(f"✓ 已移除 {removed_ann_count} 个标注")
    print(f"✓ 已移除 {removed_cat_count} 个类别定义")
    
    return data

def save_cleaned_data(data, original_path):
    """保存清理后的数据"""
    # 生成新文件名
    base_name = os.path.splitext(original_path)[0]
    extension = os.path.splitext(original_path)[1]
    new_path = f"{base_name}_no_wxxx{extension}"
    
    print(f"\n💾 保存清理后的数据集: {new_path}")
    
    with open(new_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"✓ 数据集已保存")
    return new_path

def generate_report(original_data, cleaned_data, stats, original_path, new_path):
    """生成处理报告"""
    print(f"\n📊 处理报告")
    print("=" * 50)
    
    print(f"原始文件: {original_path}")
    print(f"新文件: {new_path}")
    print(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n移除的类别:")
    print(f"  - ID: {stats['category_id']}")
    print(f"  - 名称: {stats['category_name']}")
    
    print(f"\n数据变化:")
    print(f"  - 图片数量: {len(original_data['images'])} → {len(cleaned_data['images'])} (无变化)")
    print(f"  - 标注数量: {len(original_data['annotations'])} → {len(cleaned_data['annotations'])} (-{stats['annotations_count']})")
    print(f"  - 类别数量: {len(original_data['categories'])} → {len(cleaned_data['categories'])} (-1)")
    
    print(f"\n剩余类别:")
    for cat in sorted(cleaned_data['categories'], key=lambda x: x['id']):
        print(f"  - ID {cat['id']}: {cat['name']}")
    
    print(f"\n受影响的图片数量: {stats['affected_images_count']}")
    print("注意: 这些图片仍保留在数据集中，但相关的Wxxx标注已被移除")

def main():
    """主函数"""
    print("🚀 YOLO数据集类别移除工具")
    print("=" * 50)
    
    # 数据集路径
    dataset_path = "/home/<USER>/claude/SpatialVLA/3D/yolo_final/final_3196/_annotations.coco.json"
    
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集文件不存在: {dataset_path}")
        return
    
    try:
        # 1. 备份原始文件
        backup_path = backup_file(dataset_path)
        
        # 2. 加载数据
        original_data = load_coco_data(dataset_path)
        
        # 3. 分析要移除的类别
        target_category, stats = analyze_category_to_remove(original_data, "Wxxx")
        
        if not target_category:
            return
        
        # 4. 确认操作
        print(f"\n⚠️  确认操作:")
        print(f"将要移除类别 '{stats['category_name']}' (ID: {stats['category_id']})")
        print(f"这将删除 {stats['annotations_count']} 个标注")
        print(f"影响 {stats['affected_images_count']} 张图片")
        print("✓ 自动确认继续处理...")
        
        # 5. 执行移除操作
        cleaned_data = remove_category_data(original_data.copy(), stats)
        
        # 6. 保存清理后的数据
        new_path = save_cleaned_data(cleaned_data, dataset_path)
        
        # 7. 生成报告
        generate_report(original_data, cleaned_data, stats, dataset_path, new_path)
        
        print(f"\n🎉 处理完成!")
        print(f"新的数据集文件: {new_path}")
        print(f"备份文件: {backup_path}")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
