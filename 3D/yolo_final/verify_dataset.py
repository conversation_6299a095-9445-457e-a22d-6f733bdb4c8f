#!/usr/bin/env python3
"""
验证清理后的数据集
检查移除Wxxx类别后的数据集完整性
"""

import json
import os
from pathlib import Path
from collections import Counter

def verify_dataset():
    """验证数据集完整性"""
    print("🔍 验证清理后的数据集")
    print("="*50)
    
    # 数据集路径
    dataset_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final/final_3196")
    
    # 检查两个标注文件
    original_file = dataset_dir / "_annotations.coco.json"
    cleaned_file = dataset_dir / "_annotations.coco_no_wxxx.json"
    
    if not original_file.exists():
        print(f"❌ 原始文件不存在: {original_file}")
        return
    
    if not cleaned_file.exists():
        print(f"❌ 清理后文件不存在: {cleaned_file}")
        return
    
    # 加载两个文件
    print("📖 加载标注文件...")
    with open(original_file, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    with open(cleaned_file, 'r', encoding='utf-8') as f:
        cleaned_data = json.load(f)
    
    # 比较统计信息
    print("\n📊 数据集对比:")
    print(f"{'项目':<15} {'原始数据集':<12} {'清理后数据集':<12} {'变化':<10}")
    print("-" * 55)
    
    # 图片数量
    orig_images = len(original_data['images'])
    clean_images = len(cleaned_data['images'])
    print(f"{'图片数量':<15} {orig_images:<12} {clean_images:<12} {clean_images-orig_images:+d}")
    
    # 标注数量
    orig_annotations = len(original_data['annotations'])
    clean_annotations = len(cleaned_data['annotations'])
    print(f"{'标注数量':<15} {orig_annotations:<12} {clean_annotations:<12} {clean_annotations-orig_annotations:+d}")
    
    # 类别数量
    orig_categories = len(original_data['categories'])
    clean_categories = len(cleaned_data['categories'])
    print(f"{'类别数量':<15} {orig_categories:<12} {clean_categories:<12} {clean_categories-orig_categories:+d}")
    
    # 检查类别详情
    print(f"\n🏷️  原始数据集类别 ({orig_categories}个):")
    for cat in original_data['categories']:
        print(f"  - ID {cat['id']}: {cat['name']}")
    
    print(f"\n🏷️  清理后数据集类别 ({clean_categories}个):")
    for cat in cleaned_data['categories']:
        print(f"  - ID {cat['id']}: {cat['name']}")
    
    # 统计原始数据集中各类别的标注数量
    print(f"\n📈 原始数据集标注分布:")
    orig_category_counts = Counter()
    for ann in original_data['annotations']:
        orig_category_counts[ann['category_id']] += 1
    
    # 创建类别ID到名称的映射
    orig_cat_mapping = {cat['id']: cat['name'] for cat in original_data['categories']}
    
    for cat_id, count in sorted(orig_category_counts.items()):
        cat_name = orig_cat_mapping.get(cat_id, f"Unknown_{cat_id}")
        print(f"  - {cat_name} (ID {cat_id}): {count} 个标注")
    
    # 统计清理后数据集中各类别的标注数量
    print(f"\n📈 清理后数据集标注分布:")
    clean_category_counts = Counter()
    for ann in cleaned_data['annotations']:
        clean_category_counts[ann['category_id']] += 1
    
    # 创建类别ID到名称的映射
    clean_cat_mapping = {cat['id']: cat['name'] for cat in cleaned_data['categories']}
    
    for cat_id, count in sorted(clean_category_counts.items()):
        cat_name = clean_cat_mapping.get(cat_id, f"Unknown_{cat_id}")
        print(f"  - {cat_name} (ID {cat_id}): {count} 个标注")
    
    # 验证Wxxx类别是否完全移除
    print(f"\n✅ 验证结果:")
    
    # 检查是否还有category_id=8的标注
    wxxx_annotations = [ann for ann in cleaned_data['annotations'] if ann['category_id'] == 8]
    if wxxx_annotations:
        print(f"❌ 发现 {len(wxxx_annotations)} 个Wxxx类别的标注未被移除!")
    else:
        print("✓ 所有Wxxx类别的标注已成功移除")
    
    # 检查是否还有Wxxx类别定义
    wxxx_categories = [cat for cat in cleaned_data['categories'] if cat['name'] == 'Wxxx']
    if wxxx_categories:
        print(f"❌ 发现 {len(wxxx_categories)} 个Wxxx类别定义未被移除!")
    else:
        print("✓ Wxxx类别定义已成功移除")
    
    # 检查图片文件是否存在
    print(f"\n📁 检查图片文件...")
    missing_images = 0
    sample_images = cleaned_data['images'][:10]  # 检查前10张图片
    
    for img in sample_images:
        img_path = dataset_dir / img['file_name']
        if not img_path.exists():
            missing_images += 1
            print(f"❌ 图片文件不存在: {img['file_name']}")
    
    if missing_images == 0:
        print(f"✓ 抽样检查的 {len(sample_images)} 张图片文件都存在")
    else:
        print(f"❌ 发现 {missing_images} 张图片文件缺失")
    
    # 总结
    removed_annotations = orig_annotations - clean_annotations
    removed_categories = orig_categories - clean_categories
    
    print(f"\n🎯 清理总结:")
    print(f"✓ 成功移除 {removed_annotations} 个Wxxx类别的标注")
    print(f"✓ 成功移除 {removed_categories} 个类别定义")
    print(f"✓ 保留了所有 {clean_images} 张图片")
    print(f"✓ 数据集现在包含 {clean_categories} 个有效类别")
    
    if removed_annotations > 0 and removed_categories > 0:
        print(f"\n🎉 数据集清理成功! 可以开始训练了。")
        return True
    else:
        print(f"\n⚠️  数据集清理可能有问题，请检查。")
        return False

def main():
    """主函数"""
    success = verify_dataset()
    
    if success:
        print(f"\n🚀 下一步:")
        print(f"运行训练脚本: python run_yolo12n.py")
    else:
        print(f"\n🔧 请先修复数据集问题再进行训练")

if __name__ == "__main__":
    main()
