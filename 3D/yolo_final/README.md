# 多模型YOLO训练系统

## 🎯 项目概述

本项目是一个完整的多模型YOLO训练系统，支持在指定GPU上同时训练多个YOLO模型，并分开保存全套结果。

### 支持的模型
- **YOLO11**: yolo11n, yolo11s, yolo11m, yolo11l, yolo11x
- **YOLO12**: yolo12n, yolo12s, yolo12m, yolo12l, yolo12x (完全支持)

### 核心特性
- ✅ **多GPU并行训练**: 支持在指定GPU(4,5)上并行训练
- ✅ **自动数据转换**: 从COCO格式自动转换为YOLO格式
- ✅ **结果分离保存**: 每个模型的结果独立保存
- ✅ **完整训练报告**: 自动生成训练摘要和性能对比
- ✅ **一键启动**: 单个Python文件启动所有训练

## 📁 项目结构

```
/home/<USER>/claude/SpatialVLA/3D/yolo_final/
├── README.md                    # 项目说明
├── config.py                    # 训练配置文件
├── convert_dataset.py           # 数据集转换脚本
├── multi_model_trainer.py       # 多模型训练器
├── train_all_models.py          # 基础启动脚本
├── start_training.py            # 一键启动脚本 ⭐
├── dataset/                     # 转换后的YOLO数据集
│   ├── images/
│   │   ├── train/
│   │   ├── val/
│   │   └── test/
│   ├── labels/
│   │   ├── train/
│   │   ├── val/
│   │   └── test/
│   └── dataset.yaml
├── results/                     # 训练结果（按模型分离）
│   ├── yolo11n/
│   │   ├── yolo11n_training/
│   │   │   └── weights/
│   │   │       ├── best.pt
│   │   │       └── last.pt
│   │   └── yolo11n_results.json
│   ├── yolo11s/
│   └── yolo11m/
└── logs/                        # 训练日志
    ├── yolo11n_gpu4.log
    ├── yolo11s_gpu5.log
    └── yolo11m_gpu4.log
```

## 🚀 快速开始

### 一键启动训练（推荐）

```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo_final
python start_training.py
```

这将自动：
1. 检查环境和依赖
2. 转换数据集（如果需要）
3. 在GPU 4和5上训练yolo11n、yolo11s、yolo11m
4. 生成完整的训练报告

### 自定义训练

```bash
# 训练特定模型
python multi_model_trainer.py --models yolo11n yolo11s --gpus 4 5 --epochs 100

# 调整训练参数
python multi_model_trainer.py --models yolo11m --gpus 4 --epochs 200 --batch-size 8
```

## ⚙️ 配置说明

### 默认配置
- **GPU**: 4, 5
- **训练轮数**: 100 epochs
- **批次大小**: 16 (根据模型自动调整)
- **图像尺寸**: 640x640
- **数据集**: `/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal`

### 模型配置
| 模型 | 描述 | 预期大小 | 推荐批次 |
|------|------|----------|----------|
| yolo11n | Nano - 最快最小 | ~6MB | 32 |
| yolo11s | Small - 平衡 | ~22MB | 16 |
| yolo11m | Medium - 高精度 | ~50MB | 8 |
| yolo11l | Large - 更高精度 | ~87MB | 4 |
| yolo11x | Extra Large - 最高精度 | ~136MB | 2 |

## 📊 数据集信息

**源数据集**: `/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal`
- **总图像数**: 2025张RGB图像
- **数据分割**: 训练集(1620) / 验证集(202) / 测试集(203)
- **类别数**: 9个竞赛指定类别

### 识别类别
1. CA001_衣架 (日用品)
2. CA002_牙刷 (日用品)
3. CB001_果冻 (副食品)
4. CB002_长方形状饼干 (副食品)
5. CC001_罐装饮料 (饮料)
6. CC002_瓶装饮料 (饮料)
7. CD001_香蕉 (水果)
8. CD002_橙子 (水果)
9. Wxxx_未知物品 (未知类)

## 📈 训练结果

训练完成后，系统会自动生成：

### 1. 模型文件
每个模型的训练结果保存在独立目录：
```
results/yolo11n/yolo11n_training/weights/
├── best.pt      # 最佳模型
└── last.pt      # 最后一轮模型
```

### 2. 性能报告
```json
{
  "model": "yolo11n",
  "gpu": 4,
  "training_time": 3600.0,
  "map50": 0.85,
  "map50_95": 0.65,
  "training_completed": true
}
```

### 3. 训练摘要
系统会显示所有模型的对比结果：
```
模型        状态    GPU   mAP50    mAP50-95  时间(分钟)
yolo11n     完成    4     0.8500   0.6500    60.0
yolo11s     完成    5     0.8700   0.6800    75.0
yolo11m     完成    4     0.8900   0.7100    90.0
```

## 🔍 使用训练好的模型

### 推理示例
```python
from ultralytics import YOLO

# 加载最佳模型
model = YOLO('/home/<USER>/claude/SpatialVLA/3D/yolo_final/results/yolo11s/yolo11s_training/weights/best.pt')

# 单张图像推理
results = model.predict('image.jpg')

# 批量推理
results = model.predict('images_folder/')

# 实时推理
results = model.predict(source=0)  # 摄像头
```

### 模型验证
```python
# 验证模型性能
results = model.val(data='/home/<USER>/claude/SpatialVLA/3D/yolo_final/dataset/dataset.yaml')
print(f"mAP50: {results.box.map50}")
```

## 🛠️ 故障排除

### 常见问题

1. **GPU内存不足**
   ```bash
   # 减小批次大小
   python multi_model_trainer.py --batch-size 8
   ```

2. **CUDA版本不兼容**
   ```bash
   # 检查CUDA版本
   nvidia-smi
   # 重新安装对应版本的PyTorch
   ```

3. **数据集转换失败**
   ```bash
   # 手动运行转换
   python convert_dataset.py
   ```

4. **模型下载失败**
   ```bash
   # 手动下载模型
   python -c "from ultralytics import YOLO; YOLO('yolo11n.pt')"
   ```

### 日志查看
```bash
# 查看训练日志
tail -f logs/yolo11n_gpu4.log

# 查看所有日志
ls -la logs/
```

## 📋 系统要求

- **Python**: 3.8+
- **GPU**: NVIDIA GPU with CUDA support
- **内存**: 16GB+ RAM recommended
- **存储**: 10GB+ free space
- **依赖**: ultralytics, torch, torchvision

### 安装依赖
```bash
pip install ultralytics torch torchvision torchaudio
```

## 🎯 性能优化建议

1. **GPU内存优化**
   - 根据GPU内存调整批次大小
   - 使用混合精度训练

2. **训练速度优化**
   - 启用图像缓存
   - 使用多进程数据加载

3. **精度优化**
   - 增加训练轮数
   - 调整数据增强参数

---

**项目完成，开始您的多模型训练之旅！** 🚀
