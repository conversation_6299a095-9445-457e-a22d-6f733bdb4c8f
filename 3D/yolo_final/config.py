#!/usr/bin/env python3
"""
训练配置文件
"""

# 训练配置
TRAINING_CONFIG = {
    # GPU配置
    'gpus': [4, 5],  # 指定使用GPU 4和5
    
    # 训练参数
    'epochs': 100,
    'batch_size': 16,
    'image_size': 640,
    
    # 数据集路径
    'source_dataset': '/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal',
    'target_dataset': '/home/<USER>/claude/SpatialVLA/3D/yolo_final/dataset',
    
    # 结果保存路径
    'results_dir': '/home/<USER>/claude/SpatialVLA/3D/yolo_final/results',
    'logs_dir': '/home/<USER>/claude/SpatialVLA/3D/yolo_final/logs',
    
    # 要训练的模型列表（包括YOLO11和YOLO12）
    'models': [
        'yolo11n',  # YOLO11 Nano
        'yolo11s',  # YOLO11 Small
        'yolo11m',  # YOLO11 Medium
        'yolo12n',  # YOLO12 Nano
        'yolo12s',  # YOLO12 Small
        'yolo12m',  # YOLO12 Medium
    ],
    
    # 训练超参数
    'hyperparameters': {
        'optimizer': 'AdamW',
        'lr0': 0.01,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
    },
    
    # 类别映射
    'class_names': [
        'CA001_衣架',
        'CA002_牙刷', 
        'CB001_果冻',
        'CB002_长方形状饼干',
        'CC001_罐装饮料',
        'CC002_瓶装饮料',
        'CD001_香蕉',
        'CD002_橙子',
        'Wxxx_未知物品'
    ]
}

# 模型特定配置
MODEL_CONFIGS = {
    'yolo11n': {
        'description': 'YOLO11 Nano - 最小最快的模型',
        'expected_size_mb': 6,
        'recommended_batch_size': 32
    },
    'yolo11s': {
        'description': 'YOLO11 Small - 平衡速度和精度',
        'expected_size_mb': 22,
        'recommended_batch_size': 16
    },
    'yolo11m': {
        'description': 'YOLO11 Medium - 更高精度',
        'expected_size_mb': 50,
        'recommended_batch_size': 8
    },
    'yolo11l': {
        'description': 'YOLO11 Large - 高精度模型',
        'expected_size_mb': 87,
        'recommended_batch_size': 4
    },
    'yolo11x': {
        'description': 'YOLO11 Extra Large - 最高精度',
        'expected_size_mb': 136,
        'recommended_batch_size': 2
    },
    # YOLO12模型配置
    'yolo12n': {
        'description': 'YOLO12 Nano - 最新最快的模型',
        'expected_size_mb': 6,
        'recommended_batch_size': 32
    },
    'yolo12s': {
        'description': 'YOLO12 Small - 最新平衡模型',
        'expected_size_mb': 22,
        'recommended_batch_size': 16
    },
    'yolo12m': {
        'description': 'YOLO12 Medium - 最新高精度模型',
        'expected_size_mb': 50,
        'recommended_batch_size': 8
    },
    'yolo12l': {
        'description': 'YOLO12 Large - 最新高精度模型',
        'expected_size_mb': 87,
        'recommended_batch_size': 4
    },
    'yolo12x': {
        'description': 'YOLO12 Extra Large - 最新最高精度',
        'expected_size_mb': 136,
        'recommended_batch_size': 2
    }
}

def get_model_batch_size(model_name, default_batch_size=16):
    """根据模型获取推荐的批次大小"""
    if model_name in MODEL_CONFIGS:
        return MODEL_CONFIGS[model_name]['recommended_batch_size']
    return default_batch_size

def print_config():
    """打印当前配置"""
    print("训练配置:")
    print(f"  GPU: {TRAINING_CONFIG['gpus']}")
    print(f"  训练轮数: {TRAINING_CONFIG['epochs']}")
    print(f"  批次大小: {TRAINING_CONFIG['batch_size']}")
    print(f"  图像尺寸: {TRAINING_CONFIG['image_size']}")
    print(f"  模型列表: {TRAINING_CONFIG['models']}")
    print(f"  数据集: {TRAINING_CONFIG['source_dataset']}")
    print(f"  结果目录: {TRAINING_CONFIG['results_dir']}")
    
    print("\n模型信息:")
    for model in TRAINING_CONFIG['models']:
        if model in MODEL_CONFIGS:
            config = MODEL_CONFIGS[model]
            print(f"  {model}: {config['description']}")
            print(f"    预期大小: ~{config['expected_size_mb']}MB")
            print(f"    推荐批次: {config['recommended_batch_size']}")

if __name__ == "__main__":
    print_config()
