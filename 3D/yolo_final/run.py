#!/usr/bin/env python3
"""
一键启动脚本 - 最简单的启动方式
在指定GPU(4,5)上训练yolo11n、yolo11s、yolo11m
数据集来源: /home/<USER>/claude/SpatialVLA/3D/cocofinalfinal
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """一键启动多模型训练"""
    print("🚀 多模型YOLO训练系统")
    print("="*50)
    print("📊 数据集: /home/<USER>/claude/SpatialVLA/3D/cocofinalfinal")
    print("🎯 目标GPU: 4, 5")
    print("🤖 训练模型: yolo11n, yolo11s, yolo11m, yolo12n, yolo12s, yolo12m")
    print("⏱️  预计时间: 6-12小时 (6个模型)")
    print("="*50)
    
    # 检查当前目录
    #current_dir = Path.cwd()
    #expected_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final")
    
    #if current_dir != expected_dir:
       # print(f"请切换到正确目录: {expected_dir}")
        #print(f"当前目录: {current_dir}")
        #print("运行: cd /home/<USER>/claude/SpatialVLA/3D/yolo_final")
        #return
    
    # 检查Python环境
    print("检查Python环境...")
    try:
        import torch
        print(f"✓ PyTorch: {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ GPU数量: {torch.cuda.device_count()}")
    except ImportError:
        print("✗ PyTorch未安装")
        print("请运行: pip install torch torchvision torchaudio")
        return
    
    try:
        import ultralytics
        print(f"✓ Ultralytics: {ultralytics.__version__}")
    except ImportError:
        print("✗ Ultralytics未安装")
        print("请运行: pip install ultralytics")
        return
    
    # 确认开始训练
    print("\n准备开始训练...")
    print("这将在GPU 4和5上训练6个YOLO模型 (YOLO11: n,s,m + YOLO12: n,s,m)")
    print("预计需要6-12小时完成")
    
    response = input("\n是否开始训练? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("训练已取消")
        return
    
    # 启动训练
    print("\n🎯 启动多模型训练...")
    try:
        # 运行训练脚本
        result = subprocess.run([
            sys.executable, 'start_training.py'
        ], check=True)
        
        print("\n🎉 训练完成!")
        print("📁 结果保存在: results/ 目录")
        print("📋 日志保存在: logs/ 目录")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败: {e}")
        print("请检查错误信息并重试")
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
    except Exception as e:
        print(f"\n💥 意外错误: {e}")

if __name__ == "__main__":
    main()
