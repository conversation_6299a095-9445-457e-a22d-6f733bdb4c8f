#!/usr/bin/env python3
"""
简化版YOLO12n训练脚本
直接使用ultralytics命令行接口训练
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """使用ultralytics CLI训练YOLO12n"""
    
    # 检查数据集是否已转换
    dataset_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final/yolo12n_dataset")
    dataset_yaml = dataset_dir / "dataset.yaml"
    
    if not dataset_yaml.exists():
        print("数据集未准备，先运行完整脚本...")
        print("python train_yolo12n_final.py")
        return
    
    print("使用已准备的数据集训练YOLO12n...")
    print(f"数据集配置: {dataset_yaml}")
    
    # 构建训练命令
    cmd = [
        "yolo", "train",
        f"data={dataset_yaml}",
        "model=yolo12n.pt",
        "epochs=100",
        "batch=32",
        "device=auto",
        "project=/home/<USER>/claude/SpatialVLA/3D/yolo_final/results",
        "name=yolo12n_final_3196",
        "save=True",
        "cache=True"
    ]
    
    print("执行命令:")
    print(" ".join(cmd))
    
    try:
        # 执行训练命令
        result = subprocess.run(cmd, check=True)
        print("\n训练完成!")
        
    except subprocess.CalledProcessError as e:
        print(f"训练失败: {e}")
    except FileNotFoundError:
        print("yolo命令未找到，请确保ultralytics已正确安装")
        print("或使用: python train_yolo12n_final.py")

if __name__ == "__main__":
    main()
