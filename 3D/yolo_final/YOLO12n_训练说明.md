# YOLO12n 专用训练系统

## 🎯 项目概述

基于您的反馈，YOLO12n效果最好，现在使用新的final_3196数据集专门训练YOLO12n模型。

### 📊 数据集信息
- **数据集路径**: `/home/<USER>/claude/SpatialVLA/3D/yolo_final/final_3196`
- **图像数量**: 3196张 (640x480像素)
- **格式**: COCO格式标注
- **类别**: 9个竞赛指定类别

### 🤖 模型配置
- **模型**: YOLO12n (最佳效果)
- **预期大小**: ~6MB
- **推荐批次**: 32
- **预计训练时间**: 2-4小时

## 🚀 运行指令

### 方法1: 一键启动（推荐）
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo_final
python run_yolo12n.py
```

### 方法2: 完整训练脚本
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo_final
python train_yolo12n_final.py
```

### 方法3: 使用ultralytics CLI
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo_final

# 先准备数据集
python train_yolo12n_final.py

# 然后使用CLI训练
yolo train data=yolo12n_dataset/dataset.yaml model=yolo12n.pt epochs=100 batch=32 device=auto project=results name=yolo12n_final_3196
```

### 方法4: 直接命令行
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo_final
python -c "
from ultralytics import YOLO
import sys
sys.path.append('.')
from train_yolo12n_final import prepare_dataset, train_yolo12n

# 准备数据集
dataset_yaml = prepare_dataset()

# 训练模型
train_yolo12n(dataset_yaml, epochs=100, batch_size=32, device='auto')
"
```

## 📁 训练结果

训练完成后，结果将保存在：

```
/home/<USER>/claude/SpatialVLA/3D/yolo_final/results/yolo12n_final_3196/
├── weights/
│   ├── best.pt          # 最佳模型 ⭐
│   └── last.pt          # 最后一轮模型
├── train_batch*.jpg     # 训练可视化
├── val_batch*.jpg       # 验证可视化
├── results.png          # 训练曲线
├── confusion_matrix.png # 混淆矩阵
└── args.yaml           # 训练参数
```

## 🔍 使用训练好的模型

### 推理示例
```python
from ultralytics import YOLO

# 加载最佳模型
model = YOLO('/home/<USER>/claude/SpatialVLA/3D/yolo_final/results/yolo12n_final_3196/weights/best.pt')

# 单张图像推理
results = model.predict('test_image.jpg')

# 批量推理
results = model.predict('test_images_folder/')

# 实时摄像头推理
results = model.predict(source=0)

# 保存结果
results = model.predict('image.jpg', save=True)
```

### 模型验证
```python
# 验证模型性能
results = model.val(data='/home/<USER>/claude/SpatialVLA/3D/yolo_final/yolo12n_dataset/dataset.yaml')
print(f"mAP50: {results.box.map50:.4f}")
print(f"mAP50-95: {results.box.map:.4f}")
```

### 导出模型
```python
# 导出为ONNX格式（推理加速）
model.export(format='onnx')

# 导出为TensorRT格式（NVIDIA GPU加速）
model.export(format='engine')
```

## 📊 预期性能

基于3196张图像的训练，预期性能：

| 指标 | 预期值 |
|------|--------|
| mAP50 | 0.85-0.92 |
| mAP50-95 | 0.65-0.75 |
| 推理速度 | 2-5ms/张 (GPU) |
| 模型大小 | ~6MB |
| 训练时间 | 2-4小时 |

## 🛠️ 故障排除

### 常见问题

1. **数据集路径错误**
   ```bash
   # 检查数据集是否存在
   ls -la /home/<USER>/claude/SpatialVLA/3D/yolo_final/final_3196/
   ```

2. **GPU内存不足**
   ```bash
   # 减小批次大小
   python train_yolo12n_final.py  # 脚本会自动调整
   ```

3. **YOLO12n模型下载失败**
   ```bash
   # 手动下载
   python -c "from ultralytics import YOLO; YOLO('yolo12n.pt')"
   ```

4. **权限问题**
   ```bash
   # 确保有写入权限
   chmod +x *.py
   ```

### 监控训练进度

```bash
# 实时查看训练日志
tail -f results/yolo12n_final_3196/train.log

# 监控GPU使用情况
nvidia-smi -l 1

# 使用TensorBoard查看训练曲线
tensorboard --logdir results/yolo12n_final_3196
```

## 🎯 优化建议

### 提高精度
1. **增加训练轮数**: `epochs=200`
2. **调整学习率**: `lr0=0.005`
3. **使用更大模型**: 如果精度不够，可考虑yolo12s

### 提高速度
1. **减小图像尺寸**: `imgsz=416`
2. **导出ONNX格式**: 推理加速
3. **使用TensorRT**: NVIDIA GPU专用加速

### 数据增强
脚本已包含最佳数据增强配置：
- 水平翻转: 50%
- 色彩调整: HSV增强
- Mosaic增强: 100%

## 📞 技术支持

如果遇到问题，请检查：
1. Python环境和依赖包
2. CUDA和GPU驱动
3. 数据集路径和权限
4. 磁盘空间（至少5GB）

---

## 🎉 开始训练

选择任一方法开始训练：

**最简单**: `python run_yolo12n.py`

**最直接**: `python train_yolo12n_final.py`

预计2-4小时后，您将获得一个高性能的YOLO12n模型！

**祝您训练顺利！** 🚀
