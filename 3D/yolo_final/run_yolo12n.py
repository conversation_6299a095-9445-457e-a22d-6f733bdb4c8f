#!/usr/bin/env python3
"""
一键启动YOLO12n训练
使用final_3196数据集训练YOLO12n模型
"""

import os
import sys
from pathlib import Path

def main():
    """一键启动YOLO12n训练"""
    print("🚀 YOLO12n 专用训练系统")
    print("="*50)
    print("📊 数据集: /home/<USER>/claude/SpatialVLA/3D/yolo_final/final_3196")
    print("🤖 模型: YOLO12n (最佳效果)")
    print("📈 图像数量: 3196张")
    print("⏱️  预计时间: 2-4小时")
    print("="*50)
    
    # 检查当前目录
    #current_dir = Path.cwd()
    #expected_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final")
    
    #if current_dir != expected_dir:
    #    print(f"请切换到正确目录: {expected_dir}")
    #    print(f"当前目录: {current_dir}")
    #    print("运行: cd /home/<USER>/claude/SpatialVLA/3D/yolo_final")
    #    return
    
    # 检查数据集
    dataset_dir = Path("/home/<USER>/claude/SpatialVLA/3D/yolo_final/final_3196")
    if not dataset_dir.exists():
        print(f"错误: 数据集目录不存在: {dataset_dir}")
        return
    
    annotations_file = dataset_dir / "_annotations.coco.json"
    if not annotations_file.exists():
        print(f"错误: 标注文件不存在: {annotations_file}")
        return
    
    print("✓ 数据集检查通过")
    
    # 检查Python环境
    print("检查Python环境...")
    try:
        import torch
        print(f"✓ PyTorch: {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ GPU: {torch.cuda.get_device_name(0)}")
    except ImportError:
        print("✗ PyTorch未安装")
        print("请运行: pip install torch torchvision torchaudio")
        return
    
    try:
        import ultralytics
        print(f"✓ Ultralytics: {ultralytics.__version__}")
    except ImportError:
        print("✗ Ultralytics未安装")
        print("请运行: pip install ultralytics")
        return
    
    # 确认开始训练
    print("\n准备开始YOLO12n训练...")
    print("这将使用3196张图像训练YOLO12n模型")
    print("预计需要2-4小时完成")
    
    response = input("\n是否开始训练? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("训练已取消")
        return
    
    # 启动训练
    print("\n🎯 启动YOLO12n训练...")
    try:
        # 运行训练脚本
        import subprocess
        result = subprocess.run([
            sys.executable, 'train_yolo12n_final.py'
        ], check=True)
        
        print("\n🎉 训练完成!")
        print("📁 结果保存在: results/yolo12n_final_3196/")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败: {e}")
        print("请检查错误信息并重试")
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
    except Exception as e:
        print(f"\n💥 意外错误: {e}")

if __name__ == "__main__":
    main()
