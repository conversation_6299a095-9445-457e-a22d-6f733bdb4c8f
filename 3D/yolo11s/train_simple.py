#!/usr/bin/env python3
"""
简化版YOLOv11s训练脚本
精简输出，专注于训练结果
"""

import sys
import argparse
from pathlib import Path
import torch
from ultralytics import YOLO
import yaml

def main():
    parser = argparse.ArgumentParser(description='Simple YOLOv11s Training')
    parser.add_argument('--data', type=str, 
                       default='/home/<USER>/claude/SpatialVLA/3D/yolo11s/dataset/dataset.yaml',
                       help='Dataset YAML file')
    parser.add_argument('--epochs', type=int, default=100, help='Training epochs')
    parser.add_argument('--batch-size', type=int, default=16, help='Batch size')
    parser.add_argument('--device', type=str, default='0', help='Device')
    parser.add_argument('--name', type=str, default='yolo11s_rgb', help='Experiment name')
    
    args = parser.parse_args()
    
    # 检查数据集
    if not Path(args.data).exists():
        print(f"错误: 数据集文件不存在: {args.data}")
        sys.exit(1)
    
    with open(args.data, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print(f"开始训练: {config['nc']} 类别, {args.epochs} epochs")
    
    # 加载模型
    model = YOLO('yolo11s.pt')
    
    # 训练参数
    train_args = {
        'data': args.data,
        'epochs': args.epochs,
        'batch': args.batch_size,
        'device': args.device,
        'project': 'runs/train',
        'name': args.name,
        'verbose': False,  # 减少输出
        'save': True,
        'cache': True,
        'optimizer': 'AdamW',
        'lr0': 0.01,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
    }
    
    # 开始训练
    try:
        results = model.train(**train_args)
        
        # 验证模型
        best_model_path = Path('runs/train') / args.name / 'weights' / 'best.pt'
        if best_model_path.exists():
            print(f"\n验证模型: {best_model_path}")
            model = YOLO(str(best_model_path))
            val_results = model.val(data=args.data, verbose=False)
            
            print(f"训练完成! 最佳模型: {best_model_path}")
        
    except Exception as e:
        print(f"训练失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
