#!/usr/bin/env python3
"""
YOLOv11s 推理脚本
用于机器人视觉竞赛3D识别项目
支持单张图像、批量图像和实时摄像头推理
"""

import os
import sys
import argparse
import cv2
import numpy as np
from pathlib import Path
import time
import json
from ultralytics import YOLO

class YOLOInference:
    def __init__(self, model_path, conf_threshold=0.25, iou_threshold=0.45):
        """
        初始化YOLO推理器
        
        Args:
            model_path: 模型文件路径
            conf_threshold: 置信度阈值
            iou_threshold: NMS IoU阈值
        """
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        
        # 加载模型
        print(f"加载模型: {model_path}")
        self.model = YOLO(model_path)
        
        # 获取类别名称
        self.class_names = self.model.names
        print(f"检测类别: {self.class_names}")
        
        # 为每个类别分配颜色
        np.random.seed(42)
        self.colors = np.random.randint(0, 255, size=(len(self.class_names), 3), dtype=np.uint8)
    
    def predict_image(self, image_path, save_path=None, show_conf=True):
        """
        对单张图像进行推理
        
        Args:
            image_path: 输入图像路径
            save_path: 结果保存路径
            show_conf: 是否显示置信度
        
        Returns:
            results: 检测结果
        """
        if not Path(image_path).exists():
            print(f"错误: 图像文件不存在: {image_path}")
            return None
        
        # 推理
        start_time = time.time()
        results = self.model(image_path, conf=self.conf_threshold, iou=self.iou_threshold)
        inference_time = time.time() - start_time
        
        print(f"推理时间: {inference_time:.3f}s")
        
        # 处理结果
        result = results[0]
        detections = []
        
        if result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()  # x1, y1, x2, y2
            confidences = result.boxes.conf.cpu().numpy()
            class_ids = result.boxes.cls.cpu().numpy().astype(int)
            
            for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                x1, y1, x2, y2 = box.astype(int)
                class_name = self.class_names[cls_id]
                
                detection = {
                    'class_id': int(cls_id),
                    'class_name': class_name,
                    'confidence': float(conf),
                    'bbox': [int(x1), int(y1), int(x2), int(y2)]
                }
                detections.append(detection)
                
                print(f"检测到: {class_name} (置信度: {conf:.3f}) 位置: [{x1}, {y1}, {x2}, {y2}]")
        
        # 可视化结果
        if save_path or show_conf:
            annotated_image = self.draw_detections(image_path, detections)
            
            if save_path:
                cv2.imwrite(save_path, annotated_image)
                print(f"结果已保存: {save_path}")
            
            if show_conf:
                cv2.imshow('YOLOv11 Detection', annotated_image)
                cv2.waitKey(0)
                cv2.destroyAllWindows()
        
        return {
            'image_path': image_path,
            'inference_time': inference_time,
            'detections': detections
        }
    
    def draw_detections(self, image_path, detections):
        """
        在图像上绘制检测结果
        """
        image = cv2.imread(image_path)
        
        for det in detections:
            x1, y1, x2, y2 = det['bbox']
            class_name = det['class_name']
            confidence = det['confidence']
            class_id = det['class_id']
            
            # 获取颜色
            color = tuple(map(int, self.colors[class_id]))
            
            # 绘制边界框
            cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            
            # 标签背景
            cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            
            # 标签文字
            cv2.putText(image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        return image
    
    def predict_batch(self, input_dir, output_dir, image_extensions=['.jpg', '.jpeg', '.png', '.bmp']):
        """
        批量推理
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 获取所有图像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"在 {input_dir} 中未找到图像文件")
            return
        
        print(f"找到 {len(image_files)} 张图像")
        
        all_results = []
        total_time = 0
        
        for image_file in image_files:
            print(f"\n处理: {image_file.name}")
            
            # 推理
            result = self.predict_image(
                str(image_file), 
                str(output_path / f"result_{image_file.name}"),
                show_conf=False
            )
            
            if result:
                all_results.append(result)
                total_time += result['inference_time']
        
        # 保存批量结果
        results_json = output_path / 'batch_results.json'
        with open(results_json, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n批量推理完成:")
        print(f"  处理图像: {len(all_results)}")
        print(f"  总时间: {total_time:.3f}s")
        print(f"  平均时间: {total_time/len(all_results):.3f}s/张")
        print(f"  结果保存: {output_path}")
        print(f"  详细结果: {results_json}")
    
    def predict_camera(self, camera_id=0, save_video=None):
        """
        实时摄像头推理
        """
        cap = cv2.VideoCapture(camera_id)
        
        if not cap.isOpened():
            print(f"错误: 无法打开摄像头 {camera_id}")
            return
        
        # 设置摄像头参数
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        # 视频录制设置
        if save_video:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(save_video, fourcc, 20.0, (640, 480))
        
        print("开始实时推理，按 'q' 退出")
        
        fps_counter = 0
        fps_start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 推理
            start_time = time.time()
            results = self.model(frame, conf=self.conf_threshold, iou=self.iou_threshold)
            inference_time = time.time() - start_time
            
            # 绘制结果
            result = results[0]
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                class_ids = result.boxes.cls.cpu().numpy().astype(int)
                
                for box, conf, cls_id in zip(boxes, confidences, class_ids):
                    x1, y1, x2, y2 = box.astype(int)
                    class_name = self.class_names[cls_id]
                    color = tuple(map(int, self.colors[cls_id]))
                    
                    # 绘制边界框
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    
                    # 绘制标签
                    label = f"{class_name}: {conf:.2f}"
                    cv2.putText(frame, label, (x1, y1 - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 显示FPS
            fps_counter += 1
            if fps_counter % 30 == 0:
                fps = 30 / (time.time() - fps_start_time)
                fps_start_time = time.time()
                print(f"FPS: {fps:.1f}, 推理时间: {inference_time*1000:.1f}ms")
            
            cv2.putText(frame, f"Inference: {inference_time*1000:.1f}ms", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示画面
            cv2.imshow('YOLOv11 Real-time Detection', frame)
            
            # 保存视频
            if save_video:
                out.write(frame)
            
            # 退出条件
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        if save_video:
            out.release()
        cv2.destroyAllWindows()

def main():
    parser = argparse.ArgumentParser(description='YOLOv11s Inference Script')
    parser.add_argument('--model', type=str, required=True,
                       help='Path to trained model (.pt file)')
    parser.add_argument('--source', type=str,
                       help='Source: image file, directory, or camera (0, 1, ...)')
    parser.add_argument('--output', type=str, default='runs/inference',
                       help='Output directory')
    parser.add_argument('--conf', type=float, default=0.25,
                       help='Confidence threshold')
    parser.add_argument('--iou', type=float, default=0.45,
                       help='NMS IoU threshold')
    parser.add_argument('--camera', action='store_true',
                       help='Use camera for real-time inference')
    parser.add_argument('--save-video', type=str,
                       help='Save camera output as video')
    parser.add_argument('--no-show', action='store_true',
                       help='Do not show results')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not Path(args.model).exists():
        print(f"错误: 模型文件不存在: {args.model}")
        sys.exit(1)
    
    # 创建推理器
    inferencer = YOLOInference(args.model, args.conf, args.iou)
    
    if args.camera:
        # 摄像头推理
        camera_id = int(args.source) if args.source and args.source.isdigit() else 0
        inferencer.predict_camera(camera_id, args.save_video)
    elif args.source:
        source_path = Path(args.source)
        
        if source_path.is_file():
            # 单张图像推理
            output_file = Path(args.output) / f"result_{source_path.name}"
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            inferencer.predict_image(
                str(source_path), 
                str(output_file) if not args.no_show else None,
                show_conf=not args.no_show
            )
        elif source_path.is_dir():
            # 批量推理
            inferencer.predict_batch(str(source_path), args.output)
        else:
            print(f"错误: 源路径不存在: {args.source}")
    else:
        print("错误: 请指定 --source 或使用 --camera")
        parser.print_help()

if __name__ == "__main__":
    main()