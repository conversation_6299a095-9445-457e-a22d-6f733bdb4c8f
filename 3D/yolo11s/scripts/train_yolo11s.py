#!/usr/bin/env python3
"""
YOLOv11s 训练脚本
用于机器人视觉竞赛3D识别项目
"""

import os
import sys
import argparse
from pathlib import Path
import torch
from ultralytics import YOLO
import yaml

def check_environment():
    """检查训练环境"""
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
    else:
        print("使用CPU训练")

    try:
        from ultralytics import __version__
        print(f"Ultralytics: {__version__}")
    except ImportError:
        print("错误: Ultralytics未安装")
        return False

    return True

def load_dataset_config(dataset_yaml):
    """加载数据集配置"""
    if not Path(dataset_yaml).exists():
        print(f"错误: 数据集配置文件不存在: {dataset_yaml}")
        return None

    with open(dataset_yaml, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    print(f"数据集: {config['nc']} 类别")

    return config

def train_yolo11s(dataset_yaml, epochs=100, batch_size=16, img_size=640,
                  device='auto', workers=8, project='runs/train', name='yolo11s_rgb'):
    """
    训练YOLOv11s模型
    """
    print(f"\n开始训练: {epochs} epochs, batch_size={batch_size}")

    # 加载预训练模型
    model = YOLO('yolo11s.pt')  # 自动下载预训练权重
    
    # 训练参数
    train_args = {
        'data': dataset_yaml,
        'epochs': epochs,
        'batch': batch_size,
        'imgsz': img_size,
        'device': device,
        'workers': workers,
        'project': project,
        'name': name,
        'save': True,
        'save_period': 10,  # 每10个epoch保存一次
        'cache': True,  # 缓存图像以加速训练
        'optimizer': 'AdamW',
        'lr0': 0.01,  # 初始学习率
        'lrf': 0.01,  # 最终学习率因子
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'box': 7.5,  # box loss gain
        'cls': 0.5,  # cls loss gain
        'dfl': 1.5,  # dfl loss gain
        'pose': 12.0,  # pose loss gain
        'kobj': 1.0,  # keypoint obj loss gain
        'label_smoothing': 0.0,
        'nbs': 64,  # nominal batch size
        'hsv_h': 0.015,  # 色调增强
        'hsv_s': 0.7,    # 饱和度增强
        'hsv_v': 0.4,    # 明度增强
        'degrees': 0.0,  # 旋转角度
        'translate': 0.1,  # 平移
        'scale': 0.5,    # 缩放
        'shear': 0.0,    # 剪切
        'perspective': 0.0,  # 透视变换
        'flipud': 0.0,   # 上下翻转概率
        'fliplr': 0.5,   # 左右翻转概率
        'mosaic': 1.0,   # mosaic增强概率
        'mixup': 0.0,    # mixup增强概率
        'copy_paste': 0.0,  # copy-paste增强概率
        'auto_augment': 'randaugment',  # 自动增强策略
        'erasing': 0.4,  # 随机擦除概率
        'crop_fraction': 1.0,  # 裁剪比例
    }
    
    # 开始训练
    try:
        results = model.train(**train_args)
        print("训练完成！")
        return results
    except Exception as e:
        print(f"训练失败: {e}")
        return None

def validate_model(model_path, dataset_yaml, img_size=640):
    """验证训练好的模型"""
    print("\n模型验证...")

    if not Path(model_path).exists():
        print(f"错误: 模型文件不存在: {model_path}")
        return None

    # 加载训练好的模型
    model = YOLO(model_path)

    # 在验证集上评估
    results = model.val(data=dataset_yaml, imgsz=img_size, split='val', verbose=False)

    return results

def export_model(model_path, formats=['onnx', 'torchscript']):
    """导出模型为不同格式"""
    print("\n" + "=" * 60)
    print("模型导出")
    print("=" * 60)
    
    if not Path(model_path).exists():
        print(f"错误: 模型文件不存在: {model_path}")
        return
    
    model = YOLO(model_path)
    
    for fmt in formats:
        try:
            model.export(format=fmt)
            print(f"✓ 导出{fmt}格式成功")
        except Exception as e:
            print(f"✗ 导出{fmt}格式失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='YOLOv11s Training Script for Robot Vision Competition')
    parser.add_argument('--data', type=str, 
                       default='/home/<USER>/claude/SpatialVLA/3D/yolo11s/dataset/dataset.yaml',
                       help='Dataset YAML file path')
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=16,
                       help='Batch size')
    parser.add_argument('--img-size', type=int, default=640,
                       help='Image size')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, 0, 1, ...)')
    parser.add_argument('--workers', type=int, default=8,
                       help='Number of worker threads')
    parser.add_argument('--project', type=str, default='runs/train',
                       help='Project directory')
    parser.add_argument('--name', type=str, default='yolo11s_rgb',
                       help='Experiment name')
    parser.add_argument('--validate', action='store_true',
                       help='Only validate existing model')
    parser.add_argument('--model', type=str,
                       help='Model path for validation')
    parser.add_argument('--export', action='store_true',
                       help='Export model after training')
    
    args = parser.parse_args()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)

    # 加载数据集配置
    dataset_config = load_dataset_config(args.data)
    if dataset_config is None:
        sys.exit(1)
    
    if args.validate:
        # 仅验证模式
        if args.model is None:
            print("错误: 验证模式需要指定 --model 参数")
            sys.exit(1)
        validate_model(args.model, args.data, args.img_size)
    else:
        # 训练模式
        results = train_yolo11s(
            dataset_yaml=args.data,
            epochs=args.epochs,
            batch_size=args.batch_size,
            img_size=args.img_size,
            device=args.device,
            workers=args.workers,
            project=args.project,
            name=args.name
        )
        
        if results is not None:
            # 获取最佳模型路径
            best_model = Path(args.project) / args.name / 'weights' / 'best.pt'
            
            # 验证最佳模型
            if best_model.exists():
                validate_model(str(best_model), args.data, args.img_size)
                
                # 导出模型
                if args.export:
                    export_model(str(best_model))
            
            print(f"\n✓ 训练结果保存在: {Path(args.project) / args.name}")
            print(f"✓ 最佳模型: {best_model}")

if __name__ == "__main__":
    main()