#!/usr/bin/env python3
"""
数据集分割脚本
将训练集按比例分割为训练集、验证集和测试集
"""

import os
import shutil
import random
from pathlib import Path
import argparse

def split_dataset(dataset_dir, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1, seed=42):
    """
    分割数据集
    
    Args:
        dataset_dir: 数据集根目录
        train_ratio: 训练集比例
        val_ratio: 验证集比例  
        test_ratio: 测试集比例
        seed: 随机种子
    """
    random.seed(seed)
    
    dataset_path = Path(dataset_dir)
    
    # 检查比例是否合理
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError("训练集、验证集、测试集比例之和必须等于1.0")
    
    # 获取所有训练图像文件
    train_images_dir = dataset_path / "images" / "train"
    train_labels_dir = dataset_path / "labels" / "train"
    
    if not train_images_dir.exists():
        raise FileNotFoundError(f"训练图像目录不存在: {train_images_dir}")
    
    if not train_labels_dir.exists():
        raise FileNotFoundError(f"训练标注目录不存在: {train_labels_dir}")
    
    # 获取所有图像文件名（不含扩展名）
    image_files = list(train_images_dir.glob("*.jpg"))
    image_stems = [f.stem for f in image_files]
    
    print(f"找到 {len(image_stems)} 个训练样本")
    
    # 检查标注文件是否存在
    missing_labels = []
    for stem in image_stems:
        label_file = train_labels_dir / f"{stem}.txt"
        if not label_file.exists():
            missing_labels.append(stem)
    
    if missing_labels:
        print(f"警告: {len(missing_labels)} 个样本缺少标注文件")
        # 移除缺少标注的样本
        image_stems = [stem for stem in image_stems if stem not in missing_labels]
    
    print(f"有效样本数: {len(image_stems)}")
    
    # 随机打乱
    random.shuffle(image_stems)
    
    # 计算分割点
    total_samples = len(image_stems)
    train_count = int(total_samples * train_ratio)
    val_count = int(total_samples * val_ratio)
    test_count = total_samples - train_count - val_count
    
    print(f"分割方案:")
    print(f"  训练集: {train_count} 样本 ({train_count/total_samples:.1%})")
    print(f"  验证集: {val_count} 样本 ({val_count/total_samples:.1%})")
    print(f"  测试集: {test_count} 样本 ({test_count/total_samples:.1%})")
    
    # 分割样本
    train_stems = image_stems[:train_count]
    val_stems = image_stems[train_count:train_count + val_count]
    test_stems = image_stems[train_count + val_count:]
    
    # 创建目标目录
    splits = {
        'train': train_stems,
        'val': val_stems,
        'test': test_stems
    }
    
    for split_name, stems in splits.items():
        if not stems:
            continue
            
        print(f"\n处理 {split_name} 集合 ({len(stems)} 样本)...")
        
        # 创建目录
        split_images_dir = dataset_path / "images" / split_name
        split_labels_dir = dataset_path / "labels" / split_name
        
        split_images_dir.mkdir(parents=True, exist_ok=True)
        split_labels_dir.mkdir(parents=True, exist_ok=True)
        
        # 如果是训练集，直接跳过（文件已经在正确位置）
        if split_name == 'train':
            # 删除不在训练集中的文件
            existing_files = list(train_images_dir.glob("*.jpg"))
            for img_file in existing_files:
                if img_file.stem not in train_stems:
                    # 删除图像文件
                    img_file.unlink()
                    # 删除对应的标注文件
                    label_file = train_labels_dir / f"{img_file.stem}.txt"
                    if label_file.exists():
                        label_file.unlink()
            continue
        
        # 移动文件到对应的分割目录
        for stem in stems:
            # 移动图像文件
            src_img = train_images_dir / f"{stem}.jpg"
            dst_img = split_images_dir / f"{stem}.jpg"
            
            if src_img.exists():
                shutil.move(str(src_img), str(dst_img))
            
            # 移动标注文件
            src_label = train_labels_dir / f"{stem}.txt"
            dst_label = split_labels_dir / f"{stem}.txt"
            
            if src_label.exists():
                shutil.move(str(src_label), str(dst_label))
    
    # 验证分割结果
    print(f"\n分割完成！验证结果:")
    for split_name in ['train', 'val', 'test']:
        images_dir = dataset_path / "images" / split_name
        labels_dir = dataset_path / "labels" / split_name
        
        if images_dir.exists():
            img_count = len(list(images_dir.glob("*.jpg")))
            label_count = len(list(labels_dir.glob("*.txt")))
            print(f"  {split_name}: {img_count} 图像, {label_count} 标注")

def main():
    parser = argparse.ArgumentParser(description='Split dataset into train/val/test')
    parser.add_argument('--dataset', type=str,
                       default='/home/<USER>/claude/SpatialVLA/3D/yolo11s/dataset',
                       help='Dataset directory')
    parser.add_argument('--train-ratio', type=float, default=0.8,
                       help='Training set ratio')
    parser.add_argument('--val-ratio', type=float, default=0.1,
                       help='Validation set ratio')
    parser.add_argument('--test-ratio', type=float, default=0.1,
                       help='Test set ratio')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    args = parser.parse_args()
    
    print("数据集分割工具")
    print("=" * 50)
    
    try:
        split_dataset(
            args.dataset,
            args.train_ratio,
            args.val_ratio,
            args.test_ratio,
            args.seed
        )
        print("\n✓ 数据集分割成功！")
    except Exception as e:
        print(f"\n✗ 分割失败: {e}")

if __name__ == "__main__":
    main()