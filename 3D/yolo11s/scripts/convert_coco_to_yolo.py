#!/usr/bin/env python3
"""
COCO to YOLO format converter and RGB dataset processor
将COCO格式的标注转换为YOLO格式，并复制RGB图像（去除深度数据）
"""

import json
import os
import shutil
from pathlib import Path
import argparse
from tqdm import tqdm

def convert_bbox_coco_to_yolo(bbox, img_width, img_height):
    """
    将COCO格式的bbox转换为YOLO格式
    COCO: [x_min, y_min, width, height] (绝对坐标)
    YOLO: [x_center, y_center, width, height] (相对坐标，0-1)
    """
    x_min, y_min, width, height = bbox
    
    # 转换为中心点坐标
    x_center = x_min + width / 2
    y_center = y_min + height / 2
    
    # 归一化到0-1
    x_center_norm = x_center / img_width
    y_center_norm = y_center / img_height
    width_norm = width / img_width
    height_norm = height / img_height
    
    return [x_center_norm, y_center_norm, width_norm, height_norm]

def process_coco_dataset(source_dir, target_dir):
    """
    处理COCO格式数据集（单个JSON文件）
    """
    print(f"\n处理COCO数据集...")

    # 路径设置
    source_images_dir = Path(source_dir) / "images"
    source_labels_file = Path(source_dir) / "_annotations.coco.json"

    # 检查源文件是否存在
    if not source_labels_file.exists():
        print(f"错误: {source_labels_file} 不存在")
        return False

    if not source_images_dir.exists():
        print(f"错误: {source_images_dir} 不存在")
        return False
    
    # 加载COCO标注文件
    print("加载COCO标注文件...")
    with open(source_labels_file, 'r', encoding='utf-8') as f:
        coco_data = json.load(f)

    # 创建图像ID到文件名的映射
    image_id_to_info = {img['id']: img for img in coco_data['images']}

    # 创建类别名称映射（保持原有的中文名称）
    category_id_to_name = {cat['id']: cat['name'] for cat in coco_data['categories']}

    # 创建完整的类别名称映射
    name_mapping = {
        'CA001': 'CA001_衣架',
        'CA002': 'CA002_牙刷',
        'CB001': 'CB001_果冻',
        'CB002': 'CB002_长方形状饼干',
        'CC001': 'CC001_罐装饮料',
        'CC002': 'CC002_瓶装饮料',
        'CD001': 'CD001_香蕉',
        'CD002': 'CD002_橙子',
        'Wxxx': 'Wxxx_未知物品'
    }

    # 创建类别ID到新索引的映射
    category_mapping = {}
    class_names = []
    for cat in coco_data['categories']:
        cat_name = cat['name']
        full_name = name_mapping.get(cat_name, cat_name)
        new_idx = len(class_names)
        category_mapping[cat['id']] = new_idx
        class_names.append(full_name)

    print(f"检测到 {len(class_names)} 个类别: {class_names}")

    # 按图像分组标注
    image_annotations = {}
    for ann in coco_data['annotations']:
        image_id = ann['image_id']
        if image_id not in image_annotations:
            image_annotations[image_id] = []
        image_annotations[image_id].append(ann)
    
    # 随机分割数据集
    import random
    random.seed(42)

    all_image_ids = list(image_id_to_info.keys())
    random.shuffle(all_image_ids)

    total_images = len(all_image_ids)
    train_count = int(total_images * 0.8)
    val_count = int(total_images * 0.1)

    train_ids = all_image_ids[:train_count]
    val_ids = all_image_ids[train_count:train_count + val_count]
    test_ids = all_image_ids[train_count + val_count:]

    splits = {
        'train': train_ids,
        'val': val_ids,
        'test': test_ids
    }

    print(f"数据分割: 训练集{len(train_ids)}, 验证集{len(val_ids)}, 测试集{len(test_ids)}")

    # 创建目标目录
    for split_name in ['train', 'val', 'test']:
        target_images_dir = Path(target_dir) / "images" / split_name
        target_labels_dir = Path(target_dir) / "labels" / split_name
        target_images_dir.mkdir(parents=True, exist_ok=True)
        target_labels_dir.mkdir(parents=True, exist_ok=True)

    # 处理每个分割
    total_processed = 0
    for split_name, image_ids in splits.items():
        if not image_ids:
            continue

        print(f"\n处理 {split_name} 数据集 ({len(image_ids)} 张图像)...")
        target_images_dir = Path(target_dir) / "images" / split_name
        target_labels_dir = Path(target_dir) / "labels" / split_name

        processed_count = 0
        for image_id in tqdm(image_ids, desc=f"转换{split_name}"):
            image_info = image_id_to_info[image_id]
            image_filename = image_info['file_name']
            img_width = image_info['width']
            img_height = image_info['height']

            # 复制图像文件
            source_image_path = source_images_dir / image_filename
            target_image_path = target_images_dir / image_filename

            if source_image_path.exists():
                shutil.copy2(source_image_path, target_image_path)
            else:
                print(f"警告: 图像文件不存在 {source_image_path}")
                continue
        
            # 转换标注
            yolo_annotations = []
            if image_id in image_annotations:
                for ann in image_annotations[image_id]:
                    category_id = ann['category_id']
                    bbox = ann['bbox']

                    # 转换类别ID
                    if category_id in category_mapping:
                        yolo_class_id = category_mapping[category_id]
                    else:
                        print(f"警告: 未知类别ID {category_id}")
                        continue

                    # 转换bbox格式
                    yolo_bbox = convert_bbox_coco_to_yolo(bbox, img_width, img_height)

                    # 创建YOLO格式的标注行
                    yolo_line = f"{yolo_class_id} {' '.join(map(str, yolo_bbox))}"
                    yolo_annotations.append(yolo_line)

            # 保存YOLO标注文件
            label_filename = Path(image_filename).stem + '.txt'
            target_label_path = target_labels_dir / label_filename

            with open(target_label_path, 'w') as f:
                f.write('\n'.join(yolo_annotations))

            processed_count += 1

        print(f"{split_name} 处理完成: {processed_count} 张图像")
        total_processed += processed_count

    print(f"\n总计处理完成: {total_processed} 张图像")
    return class_names

def create_dataset_yaml(target_dir, class_names):
    """
    创建YOLO格式的数据集配置文件
    """
    dataset_config = {
        'path': str(Path(target_dir).absolute()),
        'train': 'images/train',
        'val': 'images/val', 
        'test': 'images/test',
        'nc': len(class_names),
        'names': class_names
    }
    
    yaml_path = Path(target_dir) / 'dataset.yaml'
    
    # 手动写入YAML格式（避免依赖yaml库）
    with open(yaml_path, 'w', encoding='utf-8') as f:
        f.write(f"# RGB Dataset for YOLOv11 Training\n")
        f.write(f"# Converted from COCO format, depth data removed\n\n")
        f.write(f"path: {dataset_config['path']}\n")
        f.write(f"train: {dataset_config['train']}\n")
        f.write(f"val: {dataset_config['val']}\n")
        f.write(f"test: {dataset_config['test']}\n\n")
        f.write(f"nc: {dataset_config['nc']}\n")
        f.write(f"names: {class_names}\n")
    
    print(f"数据集配置文件已保存: {yaml_path}")

def main():
    parser = argparse.ArgumentParser(description='Convert COCO to YOLO format and remove depth data')
    parser.add_argument('--source', type=str,
                       default='/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal',
                       help='Source dataset directory')
    parser.add_argument('--target', type=str,
                       default='/home/<USER>/claude/SpatialVLA/3D/yolo11s/dataset',
                       help='Target dataset directory')
    
    args = parser.parse_args()
    
    source_dir = Path(args.source)
    target_dir = Path(args.target)
    
    print("=" * 60)
    print("COCO to YOLO 转换器 - RGB数据集处理")
    print("=" * 60)
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    print(f"任务: 转换COCO标注为YOLO格式，去除深度数据")
    
    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 处理COCO数据集
    class_names = process_coco_dataset(source_dir, target_dir)

    if class_names is None:
        print("错误: 数据集处理失败")
        return

    # 创建数据集配置文件
    create_dataset_yaml(target_dir, class_names)
    
    print("\n" + "=" * 60)
    print("转换完成！")
    print("=" * 60)
    print(f"RGB图像和YOLO标注已保存到: {target_dir}")
    print("深度数据已被移除")
    print("可以使用 dataset.yaml 进行YOLOv11训练")

if __name__ == "__main__":
    main()