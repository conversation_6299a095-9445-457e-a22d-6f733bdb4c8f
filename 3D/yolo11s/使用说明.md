# YOLOv11s RGB训练项目 - 使用说明

## 🎯 项目完成状态

✅ **已完成的工作**:
- 数据集已更新为更大的 `/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal` (2025张图像)
- 去除了所有深度数据，仅保留RGB图像
- 将COCO格式转换为YOLO格式
- 优化了训练输出，使其更加精简
- 创建了完整的训练流水线

## 📊 数据集信息

**新数据集**: `/home/<USER>/claude/SpatialVLA/3D/cocofinalfinal`
- **总图像数**: 2025张RGB图像
- **数据分割**: 训练集(1620) / 验证集(202) / 测试集(203)
- **类别数**: 9个竞赛指定类别
- **格式**: COCO → YOLO转换

## 🚀 快速开始训练

### 方法1: 精简输出训练（推荐）
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo11s
./quick_train.sh
```

### 方法2: 完整流水线
```bash
cd /home/<USER>/claude/SpatialVLA/3D/yolo11s
./start_training.sh
```

### 方法3: 手动执行
```bash
# 激活环境
source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet

# 转换数据集（如果还未转换）
python scripts/convert_coco_to_yolo.py

# 开始训练
python train_simple.py --epochs 100 --batch-size 16
```

## 📋 精简输出说明

训练输出已优化为您要求的精简格式，主要显示：

1. **训练进度**: 简洁的epoch和loss信息
2. **验证结果表格**: 类似您提供的格式
```
                   all         81        607       0.96      0.911      0.964      0.844
              CA001_衣架         51         60      0.887        0.9      0.969      0.778
              CA002_牙刷         55         67       0.92      0.851      0.918      0.689
              ...
```
3. **最终模型路径**: 训练完成后的模型位置

## 🎯 识别类别

支持9个竞赛类别：
- CA001_衣架、CA002_牙刷 (日用品)
- CB001_果冻、CB002_长方形状饼干 (副食品)
- CC001_罐装饮料、CC002_瓶装饮料 (饮料)
- CD001_香蕉、CD002_橙子 (水果)
- Wxxx_未知物品 (未知类)

## ⚙️ 训练参数

**默认配置**:
- 模型: YOLOv11s
- 训练轮数: 100 epochs
- 批次大小: 16
- 图像尺寸: 640x640
- 优化器: AdamW

**自定义参数**:
```bash
python train_simple.py --epochs 200 --batch-size 32 --device 0
```

## 📊 预期结果

- **训练时间**: 2-4小时（取决于硬件）
- **模型大小**: ~22MB
- **推理速度**: 2-5ms/张（GPU）
- **精度目标**: mAP50 > 0.8

## 🔍 训练完成后

训练完成后，最佳模型位于：
```
runs/train/yolo11s_rgb/weights/best.pt
```

**推理使用**:
```bash
# 单张图像
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --source test.jpg

# 批量处理
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --source test_images/

# 实时摄像头
python scripts/inference.py --model runs/train/yolo11s_rgb/weights/best.pt --camera
```

## 🏆 竞赛部署

**模型导出**:
```bash
python -c "from ultralytics import YOLO; YOLO('runs/train/yolo11s_rgb/weights/best.pt').export(format='onnx')"
```

**创建识别脚本**:
```bash
# 按竞赛要求创建识别.sh
cp quick_train.sh 识别.sh
# 然后修改为推理模式
```

## ❓ 常见问题

**Q: GPU内存不足？**
A: 减小batch_size: `--batch-size 8` 或 `--batch-size 4`

**Q: 如何查看训练进度？**
A: 训练过程中会显示精简的进度信息

**Q: 如何提高精度？**
A: 增加训练轮数: `--epochs 200`

---

**项目已完全准备就绪，开始您的训练之旅！** 🚀
