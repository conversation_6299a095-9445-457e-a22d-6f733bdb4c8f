#!/bin/bash

# YOLOv11s 训练启动脚本
# 用于机器人视觉竞赛3D识别项目

echo "YOLOv11s RGB训练项目 - 机器人视觉竞赛3D识别"

# 激活conda环境
source /home/<USER>/jing_audio/anaconda3/bin/activate efficientdet

# 切换到项目目录
cd /home/<USER>/claude/SpatialVLA/3D/yolo11s

# 运行主训练脚本
python run_training.py "$@"

# 显示结果
if [ -d "runs/train" ]; then
    LATEST_RUN=$(ls -t runs/train/ | head -n 1)
    if [ -n "$LATEST_RUN" ] && [ -f "runs/train/$LATEST_RUN/weights/best.pt" ]; then
        echo "最佳模型: runs/train/$LATEST_RUN/weights/best.pt"
    fi
fi